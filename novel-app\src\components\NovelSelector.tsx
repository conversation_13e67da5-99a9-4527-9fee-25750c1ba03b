'use client';

import { useState, useEffect } from 'react';
import { Novel } from '@/lib/database';
import { BookOpen, RefreshCw, Upload } from 'lucide-react';

interface NovelSelectorProps {
  selectedNovel: Novel | null;
  onNovelSelect: (novel: Novel | null) => void;
  disabled?: boolean;
}

interface NovelFile {
  filename: string;
  parsed: boolean;
  novel: Novel | null;
}

export default function NovelSelector({ selectedNovel, onNovelSelect, disabled }: NovelSelectorProps) {
  const [novels, setNovels] = useState<Novel[]>([]);
  const [availableFiles, setAvailableFiles] = useState<NovelFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [parsing, setParsing] = useState<string | null>(null);

  useEffect(() => {
    loadNovels();
  }, []);

  const loadNovels = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/novels');
      const result = await response.json();
      
      if (result.success) {
        setNovels(result.data.novels);
        setAvailableFiles(result.data.availableFiles);
      } else {
        console.error('加载小说列表失败:', result.error);
      }
    } catch (error) {
      console.error('加载小说列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleParseNovel = async (filename: string, reparse = false) => {
    setParsing(filename);
    try {
      const response = await fetch('/api/novels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filename, reparse }),
      });

      const result = await response.json();
      
      if (result.success) {
        await loadNovels(); // 重新加载列表
        alert(result.message);
      } else {
        alert(`解析失败: ${result.error}`);
      }
    } catch (error) {
      console.error('解析小说失败:', error);
      alert('解析小说失败');
    } finally {
      setParsing(null);
    }
  };

  const handleNovelSelect = (novel: Novel) => {
    if (disabled) return;
    onNovelSelect(novel);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <BookOpen className="mr-2" size={18} />
          选择小说
        </h2>
        <button
          onClick={loadNovels}
          disabled={loading || disabled}
          className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
          title="刷新列表"
        >
          <RefreshCw className={`${loading ? 'animate-spin' : ''}`} size={16} />
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8 text-gray-500">
          加载中...
        </div>
      ) : (
        <div className="space-y-2">
          {/* 已解析的小说 */}
          {novels.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">已解析的小说</h3>
              <div className="space-y-1">
                {novels.map((novel) => (
                  <div
                    key={novel.id}
                    onClick={() => handleNovelSelect(novel)}
                    className={`p-2 border rounded cursor-pointer transition-colors ${
                      selectedNovel?.id === novel.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <div className="font-medium text-gray-800 text-sm">{novel.title}</div>
                    <div className="text-xs text-gray-500">
                      {novel.chapterCount || 0} 章节 • {novel.filename}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 未解析的文件 */}
          {availableFiles.filter(file => !file.parsed).length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">未解析的文件</h3>
              <div className="space-y-1">
                {availableFiles
                  .filter(file => !file.parsed)
                  .map((file) => (
                    <div
                      key={file.filename}
                      className="p-2 border border-gray-200 rounded bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-gray-800 text-sm truncate">{file.filename}</div>
                          <div className="text-xs text-gray-500">未解析</div>
                        </div>
                        <button
                          onClick={() => handleParseNovel(file.filename)}
                          disabled={parsing === file.filename || disabled}
                          className="flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2"
                        >
                          {parsing === file.filename ? (
                            <>
                              <RefreshCw className="animate-spin mr-1" size={12} />
                              解析中
                            </>
                          ) : (
                            <>
                              <Upload className="mr-1" size={12} />
                              解析
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}

          {availableFiles.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <BookOpen className="mx-auto mb-2" size={32} />
              <p className="text-sm">novels 文件夹中没有找到小说文件</p>
              <p className="text-xs">请将 .txt 或 .md 文件放入 novels 文件夹</p>
            </div>
          )}
        </div>
      )}

      {selectedNovel && (
        <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
          <div className="text-sm text-blue-800">
            <strong>已选择:</strong> {selectedNovel.title}
          </div>
          <div className="text-xs text-blue-600">
            {selectedNovel.chapterCount || 0} 章节
          </div>
        </div>
      )}
    </div>
  );
}
