import { NextResponse } from 'next/server';
import { testGeminiConnection } from '@/lib/gemini';

// GET - 测试Gemini API连接
export async function GET() {
  try {
    const result = await testGeminiConnection();
    
    return NextResponse.json({
      success: result.success,
      error: result.error,
      details: result.details,
    });
  } catch (error) {
    console.error('测试API连接失败:', error);
    return NextResponse.json(
      { success: false, error: '测试API连接失败' },
      { status: 500 }
    );
  }
}
