{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/NovelSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel } from '@/lib/database';\nimport { BookOpen, RefreshCw, Upload } from 'lucide-react';\n\ninterface NovelSelectorProps {\n  selectedNovel: Novel | null;\n  onNovelSelect: (novel: Novel | null) => void;\n  disabled?: boolean;\n}\n\ninterface NovelFile {\n  filename: string;\n  parsed: boolean;\n  novel: Novel | null;\n}\n\nexport default function NovelSelector({ selectedNovel, onNovelSelect, disabled }: NovelSelectorProps) {\n  const [novels, setNovels] = useState<Novel[]>([]);\n  const [availableFiles, setAvailableFiles] = useState<NovelFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [parsing, setParsing] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadNovels();\n  }, []);\n\n  const loadNovels = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        setNovels(result.data.novels);\n        setAvailableFiles(result.data.availableFiles);\n      } else {\n        console.error('加载小说列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleParseNovel = async (filename: string, reparse = false) => {\n    setParsing(filename);\n    try {\n      const response = await fetch('/api/novels', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ filename, reparse }),\n      });\n\n      const result = await response.json();\n      \n      if (result.success) {\n        await loadNovels(); // 重新加载列表\n        alert(result.message);\n      } else {\n        alert(`解析失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('解析小说失败:', error);\n      alert('解析小说失败');\n    } finally {\n      setParsing(null);\n    }\n  };\n\n  const handleNovelSelect = (novel: Novel) => {\n    if (disabled) return;\n    onNovelSelect(novel);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <BookOpen className=\"mr-2\" size={18} />\n          选择小说\n        </h2>\n        <button\n          onClick={loadNovels}\n          disabled={loading || disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"刷新列表\"\n        >\n          <RefreshCw className={`${loading ? 'animate-spin' : ''}`} size={16} />\n        </button>\n      </div>\n\n      {loading ? (\n        <div className=\"text-center py-8 text-gray-500\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {/* 已解析的小说 */}\n          {novels.length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">已解析的小说</h3>\n              <div className=\"space-y-1\">\n                {novels.map((novel) => (\n                  <div\n                    key={novel.id}\n                    onClick={() => handleNovelSelect(novel)}\n                    className={`p-2 border rounded cursor-pointer transition-colors ${\n                      selectedNovel?.id === novel.id\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium text-gray-800 text-sm\">{novel.title}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      {novel.chapterCount || 0} 章节 • {novel.filename}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 未解析的文件 */}\n          {availableFiles.filter(file => !file.parsed).length > 0 && (\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-700 mb-2\">未解析的文件</h3>\n              <div className=\"space-y-1\">\n                {availableFiles\n                  .filter(file => !file.parsed)\n                  .map((file) => (\n                    <div\n                      key={file.filename}\n                      className=\"p-2 border border-gray-200 rounded bg-gray-50\"\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"min-w-0 flex-1\">\n                          <div className=\"font-medium text-gray-800 text-sm truncate\">{file.filename}</div>\n                          <div className=\"text-xs text-gray-500\">未解析</div>\n                        </div>\n                        <button\n                          onClick={() => handleParseNovel(file.filename)}\n                          disabled={parsing === file.filename || disabled}\n                          className=\"flex items-center px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 ml-2\"\n                        >\n                          {parsing === file.filename ? (\n                            <>\n                              <RefreshCw className=\"animate-spin mr-1\" size={12} />\n                              解析中\n                            </>\n                          ) : (\n                            <>\n                              <Upload className=\"mr-1\" size={12} />\n                              解析\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n\n          {availableFiles.length === 0 && (\n            <div className=\"text-center py-6 text-gray-500\">\n              <BookOpen className=\"mx-auto mb-2\" size={32} />\n              <p className=\"text-sm\">novels 文件夹中没有找到小说文件</p>\n              <p className=\"text-xs\">请将 .txt 或 .md 文件放入 novels 文件夹</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {selectedNovel && (\n        <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded\">\n          <div className=\"text-sm text-blue-800\">\n            <strong>已选择:</strong> {selectedNovel.title}\n          </div>\n          <div className=\"text-xs text-blue-600\">\n            {selectedNovel.chapterCount || 0} 章节\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAAA;AAJA;;;;AAkBe,SAAS,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAsB;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAc,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAgB;IAEtD,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI,CAAC,MAAM;gBAC5B,kBAAkB,OAAO,IAAI,CAAC,cAAc;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO,UAAkB,UAAU,KAAK;QAC/D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAQ;YAC3C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,cAAc,SAAS;gBAC7B,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QACd,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,0NAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,8OAAC;wBACC,SAAS;wBACT,UAAU,WAAW;wBACrB,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,6NAAS;4BAAC,WAAW,GAAG,UAAU,iBAAiB,IAAI;4BAAE,MAAM;;;;;;;;;;;;;;;;;YAInE,wBACC,8OAAC;gBAAI,WAAU;0BAAiC;;;;;qCAIhD,8OAAC;gBAAI,WAAU;;oBAEZ,OAAO,MAAM,GAAG,mBACf,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAC,oDAAoD,EAC9D,eAAe,OAAO,MAAM,EAAE,GAC1B,+BACA,wCACL,CAAC,EAAE,WAAW,kCAAkC,IAAI;;0DAErD,8OAAC;gDAAI,WAAU;0DAAqC,MAAM,KAAK;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,YAAY,IAAI;oDAAE;oDAAO,MAAM,QAAQ;;;;;;;;uCAV3C,MAAM,EAAE;;;;;;;;;;;;;;;;oBAmBtB,eAAe,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBACpD,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,eACE,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,MAAM,EAC3B,GAAG,CAAC,CAAC,qBACJ,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8C,KAAK,QAAQ;;;;;;sEAC1E,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,QAAQ;oDAC7C,UAAU,YAAY,KAAK,QAAQ,IAAI;oDACvC,WAAU;8DAET,YAAY,KAAK,QAAQ,iBACxB;;0EACE,8OAAC,6NAAS;gEAAC,WAAU;gEAAoB,MAAM;;;;;;4DAAM;;qFAIvD;;0EACE,8OAAC,gNAAM;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;uCApBxC,KAAK,QAAQ;;;;;;;;;;;;;;;;oBAgC7B,eAAe,MAAM,KAAK,mBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0NAAQ;gCAAC,WAAU;gCAAe,MAAM;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAM9B,+BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAO;;;;;;4BAAa;4BAAE,cAAc,KAAK;;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,YAAY,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ChapterSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Novel, Chapter } from '@/lib/database';\nimport { FileText, Info } from 'lucide-react';\n\ninterface ChapterSelectorProps {\n  novel: Novel | null;\n  selectedChapters: string;\n  onChaptersChange: (chapters: string) => void;\n  disabled?: boolean;\n}\n\nexport default function ChapterSelector({ \n  novel, \n  selectedChapters, \n  onChaptersChange, \n  disabled \n}: ChapterSelectorProps) {\n  const [chapters, setChapters] = useState<Chapter[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [previewChapters, setPreviewChapters] = useState<number[]>([]);\n\n  useEffect(() => {\n    if (novel) {\n      loadChapters(novel.id);\n    } else {\n      setChapters([]);\n      setPreviewChapters([]);\n    }\n  }, [novel]);\n\n  useEffect(() => {\n    // 解析章节范围并预览\n    if (selectedChapters && chapters.length > 0) {\n      const parsed = parseChapterRange(selectedChapters, chapters.length);\n      setPreviewChapters(parsed);\n    } else {\n      setPreviewChapters([]);\n    }\n  }, [selectedChapters, chapters]);\n\n  const loadChapters = async (novelId: string) => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/chapters?novelId=${novelId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setChapters(result.data);\n      } else {\n        console.error('加载章节列表失败:', result.error);\n      }\n    } catch (error) {\n      console.error('加载章节列表失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const parseChapterRange = (rangeStr: string, maxChapter: number): number[] => {\n    const chapters: number[] = [];\n    const parts = rangeStr.split(',').map(part => part.trim());\n    \n    for (const part of parts) {\n      if (part.includes('-')) {\n        // 范围格式 (例如: \"1-5\")\n        const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n        if (!isNaN(start) && !isNaN(end) && start <= end) {\n          for (let i = start; i <= Math.min(end, maxChapter); i++) {\n            if (i > 0 && !chapters.includes(i)) {\n              chapters.push(i);\n            }\n          }\n        }\n      } else {\n        // 单个章节\n        const chapterNum = parseInt(part);\n        if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n          chapters.push(chapterNum);\n        }\n      }\n    }\n    \n    return chapters.sort((a, b) => a - b);\n  };\n\n  const handleQuickSelect = (type: 'all' | 'first10' | 'last10') => {\n    if (disabled || chapters.length === 0) return;\n\n    let range = '';\n    switch (type) {\n      case 'all':\n        range = `1-${chapters.length}`;\n        break;\n      case 'first10':\n        range = `1-${Math.min(10, chapters.length)}`;\n        break;\n      case 'last10':\n        range = `${Math.max(1, chapters.length - 9)}-${chapters.length}`;\n        break;\n    }\n    onChaptersChange(range);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <h2 className=\"text-lg font-semibold text-gray-800 mb-3 flex items-center\">\n        <FileText className=\"mr-2\" size={18} />\n        选择章节 {novel && <span className=\"text-sm text-gray-500 ml-2\">共 {chapters.length} 章</span>}\n      </h2>\n\n      {!novel ? (\n        <div className=\"text-center py-6 text-gray-500\">\n          <FileText className=\"mx-auto mb-2\" size={32} />\n          <p className=\"text-sm\">请先选择一部小说</p>\n        </div>\n      ) : loading ? (\n        <div className=\"text-center py-6 text-gray-500 text-sm\">\n          加载中...\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {/* 章节范围输入 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              章节范围\n            </label>\n            <input\n              type=\"text\"\n              value={selectedChapters}\n              onChange={(e) => onChaptersChange(e.target.value)}\n              disabled={disabled}\n              placeholder=\"例如: 1-5,7,10-12\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n            />\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\n              <Info className=\"mr-1\" size={12} />\n              支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)\n            </div>\n          </div>\n\n          {/* 快速选择按钮 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              快速选择\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleQuickSelect('all')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                全部章节 (1-{chapters.length})\n              </button>\n              <button\n                onClick={() => handleQuickSelect('first10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                前10章\n              </button>\n              <button\n                onClick={() => handleQuickSelect('last10')}\n                disabled={disabled}\n                className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50\"\n              >\n                后10章\n              </button>\n            </div>\n          </div>\n\n          {/* 章节预览 */}\n          {previewChapters.length > 0 && (\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                将要改写的章节 ({previewChapters.length} 章)\n              </label>\n              <div className=\"max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50\">\n                <div className=\"grid grid-cols-1 gap-1 text-sm\">\n                  {previewChapters.map((chapterNum) => {\n                    const chapter = chapters.find(ch => ch.chapterNumber === chapterNum);\n                    return (\n                      <div key={chapterNum} className=\"flex items-center\">\n                        <span className=\"font-medium text-blue-600 w-12\">\n                          第{chapterNum}章\n                        </span>\n                        <span className=\"text-gray-700 truncate\">\n                          {chapter?.title || '未知标题'}\n                        </span>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 章节列表 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              所有章节 ({chapters.length} 章)\n            </label>\n            <div className=\"max-h-60 overflow-y-auto border border-gray-200 rounded-md\">\n              {chapters.map((chapter) => (\n                <div\n                  key={chapter.id}\n                  className={`p-2 border-b border-gray-100 last:border-b-0 ${\n                    previewChapters.includes(chapter.chapterNumber)\n                      ? 'bg-blue-50 border-l-4 border-l-blue-500'\n                      : 'hover:bg-gray-50'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-800 truncate\">\n                        第{chapter.chapterNumber}章 {chapter.title}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {chapter.content.length} 字符\n                      </div>\n                    </div>\n                    {previewChapters.includes(chapter.chapterNumber) && (\n                      <div className=\"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        已选择\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAAA;AAJA;;;;AAae,SAAS,gBAAgB,EACtC,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACa;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAW,EAAE;IAEnE,IAAA,kNAAS,EAAC;QACR,IAAI,OAAO;YACT,aAAa,MAAM,EAAE;QACvB,OAAO;YACL,YAAY,EAAE;YACd,mBAAmB,EAAE;QACvB;IACF,GAAG;QAAC;KAAM;IAEV,IAAA,kNAAS,EAAC;QACR,YAAY;QACZ,IAAI,oBAAoB,SAAS,MAAM,GAAG,GAAG;YAC3C,MAAM,SAAS,kBAAkB,kBAAkB,SAAS,MAAM;YAClE,mBAAmB;QACrB,OAAO;YACL,mBAAmB,EAAE;QACvB;IACF,GAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS;YAC/D,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,UAAkB;QAC3C,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEvD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,mBAAmB;gBACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;gBACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;oBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;wBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;4BAClC,SAAS,IAAI,CAAC;wBAChB;oBACF;gBACF;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,aAAa,SAAS;gBAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;oBACtG,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACrC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;QAEvC,IAAI,QAAQ;QACZ,OAAQ;YACN,KAAK;gBACH,QAAQ,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;gBAC9B;YACF,KAAK;gBACH,QAAQ,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM,GAAG;gBAC5C;YACF,KAAK;gBACH,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,GAAG,CAAC,EAAE,SAAS,MAAM,EAAE;gBAChE;QACJ;QACA,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,0NAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;oBAAM;oBACjC,uBAAS,8OAAC;wBAAK,WAAU;;4BAA6B;4BAAG,SAAS,MAAM;4BAAC;;;;;;;;;;;;;YAGhF,CAAC,sBACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0NAAQ;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;uBAEvB,wBACF,8OAAC;gBAAI,WAAU;0BAAyC;;;;;qCAIxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;kCAMvC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;;4CACX;4CACU,SAAS,MAAM;4CAAC;;;;;;;kDAE3B,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAOJ,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;oCAA+C;oCACpD,gBAAgB,MAAM;oCAAC;;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC;wCACpB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,aAAa,KAAK;wCACzD,qBACE,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDAAK,WAAU;;wDAAiC;wDAC7C;wDAAW;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;8DACb,SAAS,SAAS;;;;;;;2CALb;;;;;oCASd;;;;;;;;;;;;;;;;;kCAOR,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;oCAA+C;oCACvD,SAAS,MAAM;oCAAC;;;;;;;0CAEzB,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAEC,WAAW,CAAC,6CAA6C,EACvD,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,IAC1C,4CACA,oBACJ;kDAEF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEAAqC;gEAChD,QAAQ,aAAa;gEAAC;gEAAG,QAAQ,KAAK;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,OAAO,CAAC,MAAM;gEAAC;;;;;;;;;;;;;gDAG3B,gBAAgB,QAAQ,CAAC,QAAQ,aAAa,mBAC7C,8OAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;uCAjBzE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BjC", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成 - 多Key池管理\n// API Keys配置 - 第一个key是其他4个的4倍强度\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 4, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 3; // 最大重试次数\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `${chapterTitle}` : ''}\n// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写 - 支持多Key和重试\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 如果key在冷却中，等待一段时间\n      if (apiKey.cooldownUntil > Date.now()) {\n        const waitTime = Math.min(apiKey.cooldownUntil - Date.now(), 5000); // 最多等待5秒\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      const response = await fetch(`${GEMINI_API_URL}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.7,\n            topK: 40,\n            topP: 0.95,\n            maxOutputTokens: 8192,\n          },\n          safetySettings: [\n            {\n              category: \"HARM_CATEGORY_HARASSMENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_HATE_SPEECH\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            }\n          ]\n        }),\n      });\n\n      const processingTime = Date.now() - startTime;\n\n      if (response.status === 429) {\n        // 429错误，记录失败并尝试下一个key\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n          continue;\n        }\n      }\n\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const data = await response.json();\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      if (!data.candidates || data.candidates.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '没有收到有效的响应内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '响应内容格式错误',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: 'gemini-2.5-flash-lite',\n        processingTime,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n      lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n\n      if (attempt < MAX_RETRIES - 1) {\n        await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入和详细进度跟踪\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3 // 降低并发数以避免429错误\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 添加自定义预设规则\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n"], "names": [], "mappings": "AAAA,0BAA0B;AAC1B,gCAAgC;;;;;;;;;;;;;;;;;AAChC,MAAM,WAAW;IACf;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;CACD;AAED,QAAQ;AACR,MAAM,iBAAiB;AACvB,MAAM,gBAAgB,MAAM,WAAW;AACvC,MAAM,oBAAoB,OAAO,kBAAkB;AACnD,MAAM,cAAc,GAAG,SAAS;AAEhC,aAAa;AACb,MAAM;IACI,OAAO;WAAI;KAAS,CAAC;IAE7B,iBAAiB;IACjB,sBAAsB;QACpB,MAAM,MAAM,KAAK,GAAG;QAEpB,aAAa;QACb,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,IAAI;QAEnE,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,yBAAyB;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAC5B,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,MAAM;QAElD;QAEA,mBAAmB;QACnB,MAAM,UAAU,cAAc,MAAM,CAAC,CAAC,MAAM;YAC1C,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YACnD,MAAM,YAAY,KAAK,MAAM,GAAG,CAAC,KAAK,YAAY,GAAG,CAAC;YACtD,OAAO,WAAW,YAAY,MAAM;QACtC;QAEA,OAAO;IACT;IAEA,UAAU;IACV,YAAY,OAAe,EAAE,OAAgB,EAAE;QAC7C,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC3C,IAAI,KAAK;YACP,IAAI,YAAY;YAChB,IAAI,QAAQ,GAAG,KAAK,GAAG;YAEvB,IAAI,CAAC,SAAS;gBACZ,cAAc;gBACd,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;YACnC;QACF;IACF;IAEA,SAAS;IACT,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,MAAM,IAAI,IAAI;gBACd,cAAc,IAAI,YAAY;gBAC9B,QAAQ,IAAI,MAAM;gBAClB,aAAa,IAAI,aAAa,IAAI,KAAK,GAAG;gBAC1C,mBAAmB,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa,GAAG,KAAK,GAAG;YAC7D,CAAC;IACH;AACF;AAEA,MAAM,aAAa,IAAI;AAmBvB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,CAAC;;;AAGV,EAAE,MAAM;;AAER,EAAE,eAAe,GAAG,cAAc,GAAG,GAAG;GACrC,EAAE,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,GAAG,GAAG;;;AAGpD,EAAE,aAAa;;;;;;;;wBAQS,CAAC;AACzB;AAGO,eAAe,YAAY,OAAuB;IACvD,MAAM,YAAY,KAAK,GAAG;IAC1B,IAAI,YAAY;IAEhB,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;QACtD,IAAI;YACF,MAAM,SAAS,WAAW,mBAAmB;YAE7C,mBAAmB;YACnB,IAAI,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI;gBACrC,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI,OAAO,SAAS;gBAC7E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,MAAM,SAAS,YAAY;YAE3B,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;gBAClE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;wBAAC;4BACT,OAAO;gCAAC;oCACN,MAAM;gCACR;6BAAE;wBACJ;qBAAE;oBACF,kBAAkB;wBAChB,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,iBAAiB;oBACnB;oBACA,gBAAgB;wBACd;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;qBACD;gBACH;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,sBAAsB;gBACtB,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;gBAEpC,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;oBAC7E;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAEhE,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS;YACT,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;YAEpC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;gBACpD,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;YAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;gBACvC,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1F,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YAErD,oBAAoB;YACpB,MAAM,aAAa,KAAK,aAAa,EAAE,mBAAmB;YAE1D,OAAO;gBACL,eAAe,cAAc,IAAI;gBACjC,SAAS;gBACT,YAAY,OAAO,IAAI;gBACvB;gBACA,OAAO;gBACP;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,YAAY,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YAEtE,IAAI,UAAU,cAAc,GAAG;gBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;YAC/E;QACF;IACF;IAEA,OAAO;QACL,eAAe;QACf,SAAS;QACT,OAAO,CAAC,EAAE,EAAE,YAAY,QAAQ,EAAE,WAAW;QAC7C,gBAAgB,KAAK,GAAG,KAAK;IAC/B;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D,EAC/D,cAAsB,EAAE,gBAAgB;AAAjB;IAEvB,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;IACtH,IAAI,YAAY;IAChB,IAAI,kBAAkB;IACtB,MAAM,YAAY,KAAK,GAAG;IAE1B,aAAa;IACb,MAAM,YAAY,IAAI,UAAU;IAEhC,MAAM,iBAAiB,OAAO,SAA6D;QACzF,MAAM,UAAU,OAAO;QACvB,MAAM,mBAAmB,KAAK,GAAG;QAEjC,IAAI;YAEF,MAAM,SAAS,MAAM,YAAY;gBAC/B,cAAc,QAAQ,OAAO;gBAC7B;gBACA,cAAc,QAAQ,KAAK;gBAC3B,eAAe,QAAQ,MAAM;YAC/B;YAEA,MAAM,wBAAwB,KAAK,GAAG,KAAK;YAE3C,IAAI,OAAO,UAAU,EAAE;gBACrB,mBAAmB,OAAO,UAAU;YACtC;YAEA,MAAM,gBAAgB;gBACpB,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,aAAa;gBAC7B,OAAO,OAAO,KAAK;gBACnB,SAAS;oBACP,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,OAAO,OAAO,KAAK;oBACnB,gBAAgB;oBAChB,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;gBAC7B;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,WAAW;YACX,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,cAAc;YACd,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,gBAAgB;wBAChB,QAAQ,OAAO,UAAU;wBACzB,QAAQ,OAAO,UAAU;oBAC3B;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,mBAAmB,KAAK,GAAG;YACjC,MAAM,cAAc;gBAClB,SAAS;gBACT,SAAS;gBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACjE,SAAS;oBACP,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;oBAC3B,gBAAgB,mBAAmB;gBACrC;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,OAAO;QACT,SAAU;YACR,UAAU,OAAO;QACnB;IACF;IAEA,WAAW;IACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;IAC1E,MAAM,QAAQ,GAAG,CAAC;IAElB,OAAO;AACT;AAEA,cAAc;AACd,MAAM;IACI,QAAgB;IAChB,YAA+B,EAAE,CAAC;IAE1C,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAM,UAAyB;QAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,IAAI,CAAC,OAAO;YACZ,OAAO,QAAQ,OAAO;QACxB;QAEA,OAAO,IAAI,QAAc,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB;IACF;IAEA,UAAgB;QACd,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG;YAC7B,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK;YACpC,IAAI,SAAS;gBACX,IAAI,CAAC,OAAO;gBACZ;YACF;QACF;IACF;AACF;AAGO,eAAe;IAKpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;YACvB,SAAS;gBACP,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;gBACjC,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,cAAc;gBACzC,aAAa,WAAW,QAAQ;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YACnE,SAAS;gBACP,aAAa,WAAW,QAAQ;YAClC;QACF;IACF;AACF;AAGO,SAAS;IACd,OAAO,WAAW,QAAQ;AAC5B;AAGO,SAAS;IACd,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,YAAY,GAAG;QACnB,IAAI,QAAQ,GAAG;QACf,IAAI,aAAa,GAAG;IACtB;AACF;AAGO,IAAI,eAAe;IACxB,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;qBAIS,CAAC;IACpB;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;oBAIQ,CAAC;IACnB;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;eAIG,CAAC;IACd;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;IAClC,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RuleEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Settings, Wand2, Save } from 'lucide-react';\nimport { PRESET_RULES } from '@/lib/gemini';\n\ninterface RuleEditorProps {\n  rules: string;\n  onRulesChange: (rules: string) => void;\n  disabled?: boolean;\n  onSaveToPreset?: (rules: string) => void;\n}\n\nexport default function RuleEditor({ rules, onRulesChange, disabled, onSaveToPreset }: RuleEditorProps) {\n  const [showPresets, setShowPresets] = useState(false);\n\n  const handlePresetSelect = (presetKey: string) => {\n    const preset = PRESET_RULES[presetKey as keyof typeof PRESET_RULES];\n    if (preset) {\n      onRulesChange(preset.rules);\n      setShowPresets(false);\n    }\n  };\n\n  const handleSaveToPreset = () => {\n    if (rules.trim() && onSaveToPreset) {\n      onSaveToPreset(rules);\n    }\n  };\n\n  const presetButtons = Object.entries(PRESET_RULES).filter(([key]) => key !== 'custom');\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n          <Settings className=\"mr-2\" size={20} />\n          改写规则\n        </h2>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={handleSaveToPreset}\n            disabled={disabled || !rules.trim()}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"保存为预设\"\n          >\n            <Save size={16} />\n          </button>\n          <button\n            onClick={() => setShowPresets(!showPresets)}\n            disabled={disabled}\n            className=\"p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n            title=\"预设规则\"\n          >\n            <Wand2 size={16} />\n          </button>\n        </div>\n      </div>\n\n\n\n      {/* 预设规则 */}\n      {showPresets && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h3 className=\"font-medium text-gray-800 mb-2 text-sm\">选择预设规则</h3>\n          <div className=\"grid grid-cols-1 gap-1\">\n            {presetButtons.map(([key, preset]) => (\n              <button\n                key={key}\n                onClick={() => handlePresetSelect(key)}\n                disabled={disabled}\n                className=\"text-left p-2 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors\"\n              >\n                <div className=\"font-medium text-gray-800 text-sm\">{preset.name}</div>\n                <div className=\"text-xs text-gray-600\">{preset.description}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 规则编辑器 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          改写规则内容\n        </label>\n        <textarea\n          value={rules}\n          onChange={(e) => onRulesChange(e.target.value)}\n          disabled={disabled}\n          placeholder=\"请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;...\"\n          className=\"w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100\"\n        />\n        <div className=\"mt-2 text-xs text-gray-500\">\n          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAae,SAAS,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAmB;IACpG,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS,oIAAY,CAAC,UAAuC;QACnE,IAAI,QAAQ;YACV,cAAc,OAAO,KAAK;YAC1B,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,MAAM,IAAI,MAAM,gBAAgB;YAClC,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,oIAAY,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,QAAQ;IAE7E,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,sNAAQ;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,YAAY,CAAC,MAAM,IAAI;gCACjC,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,0MAAI;oCAAC,MAAM;;;;;;;;;;;0CAEd,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,wNAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAQlB,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBAC/B,8OAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAqC,OAAO,IAAI;;;;;;kDAC/D,8OAAC;wCAAI,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BANrD;;;;;;;;;;;;;;;;0BAcf,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Users, Plus, X } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  name: string;\n  type: '男主' | '女主' | '配角' | '反派' | '其他';\n  description: string;\n}\n\ninterface CharacterManagerProps {\n  characters: Character[];\n  onCharactersChange: (characters: Character[]) => void;\n  disabled?: boolean;\n}\n\nexport default function CharacterManager({ characters, onCharactersChange, disabled }: CharacterManagerProps) {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newCharacter, setNewCharacter] = useState<Omit<Character, 'id'>>({\n    name: '',\n    type: '其他',\n    description: ''\n  });\n\n  const handleAddCharacter = () => {\n    if (!newCharacter.name.trim()) return;\n    \n    const character: Character = {\n      id: Date.now().toString(),\n      ...newCharacter\n    };\n    \n    onCharactersChange([...characters, character]);\n    setNewCharacter({ name: '', type: '其他', description: '' });\n    setShowAddForm(false);\n  };\n\n  const handleRemoveCharacter = (id: string) => {\n    onCharactersChange(characters.filter(char => char.id !== id));\n  };\n\n  const characterTypes: Character['type'][] = ['男主', '女主', '配角', '反派', '其他'];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <h2 className=\"text-lg font-semibold text-gray-800 flex items-center\">\n          <Users className=\"mr-2\" size={18} />\n          人物设定\n        </h2>\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          disabled={disabled}\n          className=\"p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50\"\n          title=\"添加人物\"\n        >\n          <Plus size={16} />\n        </button>\n      </div>\n\n      {/* 添加人物表单 */}\n      {showAddForm && (\n        <div className=\"mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <div className=\"space-y-2\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                placeholder=\"人物名称\"\n                value={newCharacter.name}\n                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}\n                className=\"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              />\n              <select\n                value={newCharacter.type}\n                onChange={(e) => setNewCharacter({ ...newCharacter, type: e.target.value as Character['type'] })}\n                className=\"px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n              >\n                {characterTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"备注描述\"\n              value={newCharacter.description}\n              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}\n              className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n            />\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleAddCharacter}\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                添加\n              </button>\n              <button\n                onClick={() => setShowAddForm(false)}\n                className=\"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600\"\n              >\n                取消\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 人物列表 */}\n      <div className=\"space-y-2\">\n        {characters.length === 0 ? (\n          <div className=\"text-center py-4 text-gray-500 text-sm\">\n            暂无人物设定\n          </div>\n        ) : (\n          characters.map((character) => (\n            <div key={character.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"font-medium text-gray-800 text-sm\">{character.name}</span>\n                  <span className={`px-2 py-0.5 text-xs rounded ${\n                    character.type === '男主' ? 'bg-blue-100 text-blue-800' :\n                    character.type === '女主' ? 'bg-pink-100 text-pink-800' :\n                    character.type === '配角' ? 'bg-green-100 text-green-800' :\n                    character.type === '反派' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {character.type}\n                  </span>\n                </div>\n                {character.description && (\n                  <div className=\"text-xs text-gray-600 mt-1 truncate\">\n                    {character.description}\n                  </div>\n                )}\n              </div>\n              <button\n                onClick={() => handleRemoveCharacter(character.id)}\n                disabled={disabled}\n                className=\"p-1 text-gray-400 hover:text-red-600 disabled:opacity-50\"\n                title=\"删除\"\n              >\n                <X size={14} />\n              </button>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAkBe,SAAS,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAyB;IAC1G,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAwB;QACtE,MAAM;QACN,MAAM;QACN,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,IAAI;QAE/B,MAAM,YAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,GAAG,YAAY;QACjB;QAEA,mBAAmB;eAAI;YAAY;SAAU;QAC7C,gBAAgB;YAAE,MAAM;YAAI,MAAM;YAAM,aAAa;QAAG;QACxD,eAAe;IACjB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC3D;IAEA,MAAM,iBAAsC;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAE1E,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,6MAAK;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;kCAGtC,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,0MAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAKf,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;;;;;;8CAEZ,8OAAC;oCACC,OAAO,aAAa,IAAI;oCACxB,UAAU,CAAC,IAAM,gBAAgB;4CAAE,GAAG,YAAY;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAsB;oCAC9F,WAAU;8CAET,eAAe,GAAG,CAAC,CAAA,qBAClB,8OAAC;4CAAkB,OAAO;sDAAO;2CAApB;;;;;;;;;;;;;;;;sCAInB,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,aAAa,WAAW;4BAC/B,UAAU,CAAC,IAAM,gBAAgB;oCAAE,GAAG,YAAY;oCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gCAAC;4BAChF,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,8OAAC;oBAAI,WAAU;8BAAyC;;;;;2BAIxD,WAAW,GAAG,CAAC,CAAC,0BACd,8OAAC;wBAAuB,WAAU;;0CAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqC,UAAU,IAAI;;;;;;0DACnE,8OAAC;gDAAK,WAAW,CAAC,4BAA4B,EAC5C,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,8BAC1B,UAAU,IAAI,KAAK,OAAO,gCAC1B,UAAU,IAAI,KAAK,OAAO,4BAC1B,6BACA;0DACC,UAAU,IAAI;;;;;;;;;;;;oCAGlB,UAAU,WAAW,kBACpB,8OAAC;wCAAI,WAAU;kDACZ,UAAU,WAAW;;;;;;;;;;;;0CAI5B,8OAAC;gCACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;gCACjD,UAAU;gCACV,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,iMAAC;oCAAC,MAAM;;;;;;;;;;;;uBA1BH,UAAU,EAAE;;;;;;;;;;;;;;;;AAkClC", "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/RewriteProgress.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';\n\ninterface RewriteProgressProps {\n  jobId: string;\n  onComplete: () => void;\n}\n\ninterface JobStatus {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n      cooldownRemaining?: number;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\nexport default function RewriteProgress({ jobId, onComplete }: RewriteProgressProps) {\n  const [job, setJob] = useState<JobStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const interval = setInterval(checkJobStatus, 2000); // 每2秒检查一次状态\n    checkJobStatus(); // 立即检查一次\n\n    return () => clearInterval(interval);\n  }, [jobId]);\n\n  const checkJobStatus = async () => {\n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setJob(result.data);\n        setLoading(false);\n        \n        // 如果任务完成或失败，停止轮询并通知父组件\n        if (result.data.status === 'completed' || result.data.status === 'failed') {\n          setTimeout(() => {\n            onComplete();\n          }, 2000); // 2秒后通知完成\n        }\n      } else {\n        console.error('获取任务状态失败:', result.error);\n      }\n    } catch (error) {\n      console.error('获取任务状态失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={20} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={20} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return '等待处理';\n      case 'processing':\n        return '正在改写';\n      case 'completed':\n        return '改写完成';\n      case 'failed':\n        return '改写失败';\n      default:\n        return '未知状态';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n      case 'processing':\n        return 'text-blue-600 bg-blue-50 border-blue-200';\n      case 'completed':\n        return 'text-green-600 bg-green-50 border-green-200';\n      case 'failed':\n        return 'text-red-600 bg-red-50 border-red-200';\n      default:\n        return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">获取任务状态中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!job) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4 text-red-600\">\n          <XCircle className=\"mx-auto mb-2\" size={32} />\n          <p>无法获取任务状态</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">改写进度</h3>\n      \n      {/* 状态显示 */}\n      <div className={`p-4 rounded-lg border ${getStatusColor(job.status)} mb-4`}>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {getStatusIcon(job.status)}\n            <span className=\"ml-2 font-medium\">{getStatusText(job.status)}</span>\n          </div>\n          <span className=\"text-sm\">\n            {job.progress}%\n          </span>\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n          <span>进度</span>\n          <span>{job.progress}%</span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${\n              job.status === 'completed'\n                ? 'bg-green-500'\n                : job.status === 'failed'\n                ? 'bg-red-500'\n                : 'bg-blue-500'\n            }`}\n            style={{ width: `${job.progress}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* 详细统计信息 */}\n      {job.details && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n          {/* 章节统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">章节统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总章节: {job.details.totalChapters}</div>\n              <div>已完成: {job.details.completedChapters}</div>\n              <div>失败: {job.details.failedChapters}</div>\n              <div>剩余: {job.details.totalChapters - job.details.completedChapters - job.details.failedChapters}</div>\n            </div>\n          </div>\n\n          {/* 性能统计 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <h4 className=\"font-medium text-gray-800 mb-2\">性能统计</h4>\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)}秒</div>\n              <div>平均每章: {Math.round(job.details.averageTimePerChapter / 1000)}秒</div>\n              <div>Token消耗: {job.details.totalTokensUsed.toLocaleString()}</div>\n              <div>模型: {job.details.model}</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* API Key状态 */}\n      {job.details?.apiKeyStats && job.details.apiKeyStats.length > 0 && (\n        <div className=\"mb-4\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">API Key 使用状态</h4>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2\">\n            {job.details.apiKeyStats.map((keyStats, index) => (\n              <div key={index} className={`p-2 rounded border text-xs ${\n                keyStats.isAvailable ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n              }`}>\n                <div className=\"font-medium\">{keyStats.name}</div>\n                <div>权重: {keyStats.weight}x</div>\n                <div>使用次数: {keyStats.requestCount}</div>\n                <div className={keyStats.isAvailable ? 'text-green-600' : 'text-red-600'}>\n                  {keyStats.isAvailable ? '可用' : '冷却中'}\n                </div>\n                {keyStats.cooldownRemaining && keyStats.cooldownRemaining > 0 && (\n                  <div className=\"text-red-500\">\n                    冷却: {Math.round(keyStats.cooldownRemaining / 1000)}s\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 时间信息 */}\n      <div className=\"text-sm text-gray-500 space-y-1\">\n        <div>开始时间: {new Date(job.createdAt).toLocaleString()}</div>\n        <div>更新时间: {new Date(job.updatedAt).toLocaleString()}</div>\n        {job.details?.totalProcessingTime && job.status === 'completed' && (\n          <div>总耗时: {Math.round(job.details.totalProcessingTime / 1000)} 秒</div>\n        )}\n      </div>\n\n      {/* 结果信息 */}\n      {job.result && (\n        <div className=\"mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg\">\n          <h4 className=\"font-medium text-gray-800 mb-2\">结果信息</h4>\n          <p className=\"text-sm text-gray-700 whitespace-pre-wrap\">{job.result}</p>\n        </div>\n      )}\n\n      {/* 操作提示 */}\n      {job.status === 'completed' && (\n        <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n          <div className=\"flex items-center text-green-700\">\n            <CheckCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写完成！改写后的文件已保存到 data/rewritten 目录中。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'failed' && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center text-red-700\">\n            <XCircle className=\"mr-2\" size={16} />\n            <span className=\"text-sm\">\n              改写失败，请检查错误信息并重试。\n            </span>\n          </div>\n        </div>\n      )}\n\n      {job.status === 'processing' && (\n        <div className=\"mt-4 space-y-3\">\n          <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n            <div className=\"flex items-center text-blue-700\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2\"></div>\n              <span className=\"text-sm\">\n                正在使用 Gemini AI 改写章节，请耐心等待...\n              </span>\n            </div>\n            {job.details && (\n              <div className=\"mt-2 text-xs text-blue-600\">\n                并发数: {job.details.concurrency} | 模型: {job.details.model}\n              </div>\n            )}\n          </div>\n\n          {/* 最近完成的章节 */}\n          {job.details?.chapterResults && job.details.chapterResults.length > 0 && (\n            <div className=\"bg-gray-50 p-3 rounded-lg\">\n              <h4 className=\"font-medium text-gray-800 mb-2\">最近完成的章节</h4>\n              <div className=\"max-h-32 overflow-y-auto space-y-1\">\n                {job.details.chapterResults\n                  .filter(result => result.completedAt)\n                  .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime())\n                  .slice(0, 5)\n                  .map((result, index) => (\n                    <div key={index} className={`text-xs p-2 rounded ${\n                      result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'\n                    }`}>\n                      <div className=\"flex justify-between items-center\">\n                        <span>第{result.chapterNumber}章: {result.chapterTitle}</span>\n                        <span>{result.success ? '✓' : '✗'}</span>\n                      </div>\n                      <div className=\"flex justify-between text-xs opacity-75\">\n                        <span>{result.apiKeyUsed}</span>\n                        <span>{result.processingTime ? Math.round(result.processingTime / 1000) + 's' : ''}</span>\n                        <span>{result.tokensUsed ? result.tokensUsed + ' tokens' : ''}</span>\n                      </div>\n                      {result.error && (\n                        <div className=\"text-red-600 text-xs mt-1\">{result.error}</div>\n                      )}\n                    </div>\n                  ))}\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AA8Ce,SAAS,gBAAgB,EAAE,KAAK,EAAE,UAAU,EAAwB;IACjF,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,iNAAQ,EAAmB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,MAAM,WAAW,YAAY,gBAAgB,OAAO,YAAY;QAChE,kBAAkB,SAAS;QAE3B,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAM;IAEV,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,OAAO;YACvD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,IAAI;gBAClB,WAAW;gBAEX,uBAAuB;gBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;oBACzE,WAAW;wBACT;oBACF,GAAG,OAAO,UAAU;gBACtB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,OAAO,KAAK;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,6MAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,0OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,uNAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,8OAAC,mOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uNAAO;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACxC,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAGzD,8OAAC;gBAAI,WAAW,CAAC,sBAAsB,EAAE,eAAe,IAAI,MAAM,EAAE,KAAK,CAAC;0BACxE,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,IAAI,MAAM;8CACzB,8OAAC;oCAAK,WAAU;8CAAoB,cAAc,IAAI,MAAM;;;;;;;;;;;;sCAE9D,8OAAC;4BAAK,WAAU;;gCACb,IAAI,QAAQ;gCAAC;;;;;;;;;;;;;;;;;;0BAMpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;;oCAAM,IAAI,QAAQ;oCAAC;;;;;;;;;;;;;kCAEtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAW,CAAC,6CAA6C,EACvD,IAAI,MAAM,KAAK,cACX,iBACA,IAAI,MAAM,KAAK,WACf,eACA,eACJ;4BACF,OAAO;gCAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;YAMxC,IAAI,OAAO,kBACV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,aAAa;;;;;;;kDACpC,8OAAC;;4CAAI;4CAAM,IAAI,OAAO,CAAC,iBAAiB;;;;;;;kDACxC,8OAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,cAAc;;;;;;;kDACpC,8OAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;kCAKpG,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4CAAM;;;;;;;kDAC9D,8OAAC;;4CAAI;4CAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,qBAAqB,GAAG;4CAAM;;;;;;;kDACjE,8OAAC;;4CAAI;4CAAU,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;kDACzD,8OAAC;;4CAAI;4CAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC,IAAI,OAAO,EAAE,eAAe,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,mBAC5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,8OAAC;wBAAI,WAAU;kCACZ,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,sBACtC,8OAAC;gCAAgB,WAAW,CAAC,2BAA2B,EACtD,SAAS,WAAW,GAAG,iCAAiC,4BACxD;;kDACA,8OAAC;wCAAI,WAAU;kDAAe,SAAS,IAAI;;;;;;kDAC3C,8OAAC;;4CAAI;4CAAK,SAAS,MAAM;4CAAC;;;;;;;kDAC1B,8OAAC;;4CAAI;4CAAO,SAAS,YAAY;;;;;;;kDACjC,8OAAC;wCAAI,WAAW,SAAS,WAAW,GAAG,mBAAmB;kDACvD,SAAS,WAAW,GAAG,OAAO;;;;;;oCAEhC,SAAS,iBAAiB,IAAI,SAAS,iBAAiB,GAAG,mBAC1D,8OAAC;wCAAI,WAAU;;4CAAe;4CACvB,KAAK,KAAK,CAAC,SAAS,iBAAiB,GAAG;4CAAM;;;;;;;;+BAX/C;;;;;;;;;;;;;;;;0BAqBlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;kCAClD,8OAAC;;4BAAI;4BAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oBACjD,IAAI,OAAO,EAAE,uBAAuB,IAAI,MAAM,KAAK,6BAClD,8OAAC;;4BAAI;4BAAM,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,GAAG;4BAAM;;;;;;;;;;;;;YAKjE,IAAI,MAAM,kBACT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,8OAAC;wBAAE,WAAU;kCAA6C,IAAI,MAAM;;;;;;;;;;;;YAKvE,IAAI,MAAM,KAAK,6BACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0OAAW;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCACpC,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,0BACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uNAAO;4BAAC,WAAU;4BAAO,MAAM;;;;;;sCAChC,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO/B,IAAI,MAAM,KAAK,8BACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAI3B,IAAI,OAAO,kBACV,8OAAC;gCAAI,WAAU;;oCAA6B;oCACpC,IAAI,OAAO,CAAC,WAAW;oCAAC;oCAAQ,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oBAM5D,IAAI,OAAO,EAAE,kBAAkB,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,mBAClE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CACZ,IAAI,OAAO,CAAC,cAAc,CACxB,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,EACnC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAG,OAAO,IACpF,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,QAAQ,sBACZ,8OAAC;wCAAgB,WAAW,CAAC,oBAAoB,EAC/C,OAAO,OAAO,GAAG,gCAAgC,2BACjD;;0DACA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAE,OAAO,aAAa;4DAAC;4DAAI,OAAO,YAAY;;;;;;;kEACpD,8OAAC;kEAAM,OAAO,OAAO,GAAG,MAAM;;;;;;;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,OAAO,UAAU;;;;;;kEACxB,8OAAC;kEAAM,OAAO,cAAc,GAAG,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG,QAAQ,MAAM;;;;;;kEAChF,8OAAC;kEAAM,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,YAAY;;;;;;;;;;;;4CAE5D,OAAO,KAAK,kBACX,8OAAC;gDAAI,WAAU;0DAA6B,OAAO,KAAK;;;;;;;uCAblD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB9B", "debugId": null}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/JobHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Clock, AlertCircle, Eye, Trash2, RefreshCw } from 'lucide-react';\n\ninterface JobHistoryProps {\n  onJobSelect?: (jobId: string) => void;\n}\n\ninterface JobSummary {\n  id: string;\n  novelId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    model?: string;\n  };\n}\n\nexport default function JobHistory({ onJobSelect }: JobHistoryProps) {\n  const [jobs, setJobs] = useState<JobSummary[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [novels, setNovels] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    loadJobs();\n    loadNovels();\n  }, []);\n\n  const loadJobs = async () => {\n    try {\n      const response = await fetch('/api/jobs');\n      const result = await response.json();\n      \n      if (result.success) {\n        // 按创建时间倒序排列\n        const sortedJobs = result.data.sort((a: JobSummary, b: JobSummary) => \n          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n        );\n        setJobs(sortedJobs);\n      }\n    } catch (error) {\n      console.error('加载任务历史失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadNovels = async () => {\n    try {\n      const response = await fetch('/api/novels');\n      const result = await response.json();\n      \n      if (result.success) {\n        const novelMap: Record<string, string> = {};\n        result.data.forEach((novel: any) => {\n          novelMap[novel.id] = novel.title;\n        });\n        setNovels(novelMap);\n      }\n    } catch (error) {\n      console.error('加载小说列表失败:', error);\n    }\n  };\n\n  const deleteJob = async (jobId: string) => {\n    if (!confirm('确定要删除这个任务记录吗？')) return;\n    \n    try {\n      const response = await fetch(`/api/jobs?jobId=${jobId}`, {\n        method: 'DELETE',\n      });\n      \n      if (response.ok) {\n        setJobs(jobs.filter(job => job.id !== jobId));\n      }\n    } catch (error) {\n      console.error('删除任务失败:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-500\" size={16} />;\n      case 'processing':\n        return <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>;\n      case 'completed':\n        return <CheckCircle className=\"text-green-500\" size={16} />;\n      case 'failed':\n        return <XCircle className=\"text-red-500\" size={16} />;\n      default:\n        return <AlertCircle className=\"text-gray-500\" size={16} />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'pending': return '等待中';\n      case 'processing': return '处理中';\n      case 'completed': return '已完成';\n      case 'failed': return '失败';\n      default: return '未知';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending': return 'bg-yellow-50 border-yellow-200';\n      case 'processing': return 'bg-blue-50 border-blue-200';\n      case 'completed': return 'bg-green-50 border-green-200';\n      case 'failed': return 'bg-red-50 border-red-200';\n      default: return 'bg-gray-50 border-gray-200';\n    }\n  };\n\n  const formatDuration = (ms: number) => {\n    const seconds = Math.round(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.round(seconds / 60);\n    if (minutes < 60) return `${minutes}分钟`;\n    const hours = Math.round(minutes / 60);\n    return `${hours}小时`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载任务历史中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-lg font-semibold text-gray-800\">任务历史</h3>\n          <button\n            onClick={loadJobs}\n            className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800\"\n          >\n            <RefreshCw size={14} className=\"mr-1\" />\n            刷新\n          </button>\n        </div>\n      </div>\n\n      <div className=\"max-h-96 overflow-y-auto\">\n        {jobs.length === 0 ? (\n          <div className=\"p-6 text-center text-gray-500\">\n            暂无任务记录\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {jobs.map((job) => (\n              <div key={job.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center mb-2\">\n                      {getStatusIcon(job.status)}\n                      <span className=\"ml-2 font-medium text-gray-800\">\n                        {novels[job.novelId] || '未知小说'}\n                      </span>\n                      <span className={`ml-2 px-2 py-1 text-xs rounded border ${getStatusColor(job.status)}`}>\n                        {getStatusText(job.status)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-600 space-y-1\">\n                      <div>创建时间: {new Date(job.createdAt).toLocaleString()}</div>\n                      {job.details && (\n                        <div className=\"flex space-x-4\">\n                          <span>章节: {job.details.completedChapters}/{job.details.totalChapters}</span>\n                          {job.details.totalTokensUsed > 0 && (\n                            <span>Token: {job.details.totalTokensUsed.toLocaleString()}</span>\n                          )}\n                          {job.details.totalProcessingTime > 0 && (\n                            <span>耗时: {formatDuration(job.details.totalProcessingTime)}</span>\n                          )}\n                          {job.details.model && (\n                            <span>模型: {job.details.model}</span>\n                          )}\n                        </div>\n                      )}\n                      {job.status !== 'completed' && job.status !== 'failed' && (\n                        <div>进度: {job.progress}%</div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    {onJobSelect && (\n                      <button\n                        onClick={() => onJobSelect(job.id)}\n                        className=\"p-1 text-blue-600 hover:text-blue-800\"\n                        title=\"查看详情\"\n                      >\n                        <Eye size={16} />\n                      </button>\n                    )}\n                    <button\n                      onClick={() => deleteJob(job.id)}\n                      className=\"p-1 text-red-600 hover:text-red-800\"\n                      title=\"删除记录\"\n                    >\n                      <Trash2 size={16} />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA2Be,SAAS,WAAW,EAAE,WAAW,EAAmB;IACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAe,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAyB,CAAC;IAE9D,IAAA,kNAAS,EAAC;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAe,IAClD,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAEjE,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAmC,CAAC;gBAC1C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnB,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,KAAK;gBAClC;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,QAAQ,kBAAkB;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE;gBACvD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,6MAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,8OAAC;oBAAI,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,0OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,uNAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,8OAAC,mOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK;QAChC,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,EAAE,CAAC;QACvC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,6NAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;0BACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;oBAAI,WAAU;8BAAgC;;;;;yCAI/C,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAAiB,WAAU;sCAC1B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,cAAc,IAAI,MAAM;kEACzB,8OAAC;wDAAK,WAAU;kEACb,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI;;;;;;kEAE1B,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EAAE,eAAe,IAAI,MAAM,GAAG;kEACnF,cAAc,IAAI,MAAM;;;;;;;;;;;;0DAI7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;oDACjD,IAAI,OAAO,kBACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,iBAAiB;oEAAC;oEAAE,IAAI,OAAO,CAAC,aAAa;;;;;;;4DACnE,IAAI,OAAO,CAAC,eAAe,GAAG,mBAC7B,8OAAC;;oEAAK;oEAAQ,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc;;;;;;;4DAEzD,IAAI,OAAO,CAAC,mBAAmB,GAAG,mBACjC,8OAAC;;oEAAK;oEAAK,eAAe,IAAI,OAAO,CAAC,mBAAmB;;;;;;;4DAE1D,IAAI,OAAO,CAAC,KAAK,kBAChB,8OAAC;;oEAAK;oEAAK,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;;;oDAIjC,IAAI,MAAM,KAAK,eAAe,IAAI,MAAM,KAAK,0BAC5C,8OAAC;;4DAAI;4DAAK,IAAI,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;wCAAI,WAAU;;4CACZ,6BACC,8OAAC;gDACC,SAAS,IAAM,YAAY,IAAI,EAAE;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,uMAAG;oDAAC,MAAM;;;;;;;;;;;0DAGf,8OAAC;gDACC,SAAS,IAAM,UAAU,IAAI,EAAE;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,oNAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;2BAlDZ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;AA6D9B", "debugId": null}}, {"offset": {"line": 3065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/ApiKeyStats.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { RefreshCw, Key, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';\n\ninterface ApiKeyStatsProps {\n  refreshInterval?: number; // 刷新间隔（毫秒）\n}\n\ninterface ApiKeyStats {\n  name: string;\n  requestCount: number;\n  weight: number;\n  isAvailable: boolean;\n  cooldownRemaining?: number;\n}\n\nexport default function ApiKeyStats({ refreshInterval = 5000 }: ApiKeyStatsProps) {\n  const [stats, setStats] = useState<ApiKeyStats[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);\n\n  useEffect(() => {\n    loadStats();\n    \n    if (refreshInterval > 0) {\n      const interval = setInterval(loadStats, refreshInterval);\n      return () => clearInterval(interval);\n    }\n  }, [refreshInterval]);\n\n  const loadStats = async () => {\n    try {\n      const response = await fetch('/api/gemini/stats');\n      const result = await response.json();\n      \n      if (result.success) {\n        setStats(result.data);\n        setLastUpdated(new Date());\n      }\n    } catch (error) {\n      console.error('加载API统计失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testConnection = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/gemini/test');\n      const result = await response.json();\n      \n      if (result.success) {\n        alert(`连接测试成功！\\n使用的API Key: ${result.details?.apiKeyUsed}\\nToken消耗: ${result.details?.tokensUsed}\\n处理时间: ${result.details?.processingTime}ms`);\n      } else {\n        alert(`连接测试失败: ${result.error}`);\n      }\n      \n      // 测试后刷新统计\n      await loadStats();\n    } catch (error) {\n      alert(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetStats = async () => {\n    if (!confirm('确定要重置所有API Key统计吗？')) return;\n    \n    try {\n      const response = await fetch('/api/gemini/reset', {\n        method: 'POST',\n      });\n      \n      if (response.ok) {\n        await loadStats();\n        alert('统计已重置');\n      }\n    } catch (error) {\n      alert(`重置失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  const formatCooldown = (ms: number) => {\n    const seconds = Math.ceil(ms / 1000);\n    if (seconds < 60) return `${seconds}秒`;\n    const minutes = Math.ceil(seconds / 60);\n    return `${minutes}分钟`;\n  };\n\n  if (loading && stats.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <div className=\"text-center py-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">加载API统计中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <Key className=\"mr-2 text-blue-600\" size={20} />\n            <h3 className=\"text-lg font-semibold text-gray-800\">API Key 状态</h3>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {lastUpdated && (\n              <span className=\"text-xs text-gray-500\">\n                更新于 {lastUpdated.toLocaleTimeString()}\n              </span>\n            )}\n            <button\n              onClick={loadStats}\n              disabled={loading}\n              className=\"flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50\"\n            >\n              <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />\n              刷新\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {stats.length === 0 ? (\n          <div className=\"text-center text-gray-500 py-4\">\n            暂无API Key统计数据\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {/* 总体统计 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-blue-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Activity className=\"text-blue-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-blue-600\">总请求数</div>\n                    <div className=\"text-lg font-semibold text-blue-800\">\n                      {stats.reduce((sum, stat) => sum + stat.requestCount, 0)}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-green-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-green-600\">可用Key</div>\n                    <div className=\"text-lg font-semibold text-green-800\">\n                      {stats.filter(stat => stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-red-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <XCircle className=\"text-red-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-red-600\">冷却中</div>\n                    <div className=\"text-lg font-semibold text-red-800\">\n                      {stats.filter(stat => !stat.isAvailable).length}\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-50 p-3 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Key className=\"text-purple-600 mr-2\" size={16} />\n                  <div>\n                    <div className=\"text-sm text-purple-600\">总权重</div>\n                    <div className=\"text-lg font-semibold text-purple-800\">\n                      {stats.reduce((sum, stat) => sum + stat.weight, 0)}x\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 详细Key状态 */}\n            <div className=\"space-y-3\">\n              {stats.map((stat, index) => (\n                <div key={index} className={`p-4 rounded-lg border ${\n                  stat.isAvailable \n                    ? 'bg-green-50 border-green-200' \n                    : 'bg-red-50 border-red-200'\n                }`}>\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-3 h-3 rounded-full mr-3 ${\n                        stat.isAvailable ? 'bg-green-500' : 'bg-red-500'\n                      }`}></div>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{stat.name}</div>\n                        <div className=\"text-sm text-gray-600\">\n                          权重: {stat.weight}x | 使用次数: {stat.requestCount}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-right\">\n                      <div className={`text-sm font-medium ${\n                        stat.isAvailable ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.isAvailable ? '可用' : '冷却中'}\n                      </div>\n                      {!stat.isAvailable && stat.cooldownRemaining && stat.cooldownRemaining > 0 && (\n                        <div className=\"text-xs text-red-500 flex items-center\">\n                          <Clock size={12} className=\"mr-1\" />\n                          {formatCooldown(stat.cooldownRemaining)}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200\">\n          <button\n            onClick={testConnection}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? '测试中...' : '测试连接'}\n          </button>\n          \n          <button\n            onClick={resetStats}\n            className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\"\n          >\n            重置统计\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiBe,SAAS,YAAY,EAAE,kBAAkB,IAAI,EAAoB;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAc;IAE5D,IAAA,kNAAS,EAAC;QACR;QAEA,IAAI,kBAAkB,GAAG;YACvB,MAAM,WAAW,YAAY,WAAW;YACxC,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,IAAI;gBACpB,eAAe,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,CAAC,qBAAqB,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,OAAO,OAAO,EAAE,WAAW,QAAQ,EAAE,OAAO,OAAO,EAAE,eAAe,EAAE,CAAC;YAC/I,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;YAEA,UAAU;YACV,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,uBAAuB;QAEpC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK;QAC/B,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC;QACtC,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU;QACpC,OAAO,GAAG,QAAQ,EAAE,CAAC;IACvB;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK,GAAG;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uMAAG;oCAAC,WAAU;oCAAqB,MAAM;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;gCACZ,6BACC,8OAAC;oCAAK,WAAU;;wCAAwB;wCACjC,YAAY,kBAAkB;;;;;;;8CAGvC,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,6NAAS;4CAAC,MAAM;4CAAI,WAAW,CAAC,KAAK,EAAE,UAAU,iBAAiB,IAAI;;;;;;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAOnF,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAQ;oDAAC,WAAU;oDAAqB,MAAM;;;;;;8DAC/C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0OAAW;oDAAC,WAAU;oDAAsB,MAAM;;;;;;8DACnD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;sEACxC,8OAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMtD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uNAAO;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC7C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAuB;;;;;;sEACtC,8OAAC;4DAAI,WAAU;sEACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uMAAG;oDAAC,WAAU;oDAAuB,MAAM;;;;;;8DAC5C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA0B;;;;;;sEACzC,8OAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7D,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wCAAgB,WAAW,CAAC,sBAAsB,EACjD,KAAK,WAAW,GACZ,iCACA,4BACJ;kDACA,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,KAAK,WAAW,GAAG,iBAAiB,cACpC;;;;;;sEACF,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACrD,8OAAC;oEAAI,WAAU;;wEAAwB;wEAChC,KAAK,MAAM;wEAAC;wEAAW,KAAK,YAAY;;;;;;;;;;;;;;;;;;;8DAKnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,oBAAoB,EACnC,KAAK,WAAW,GAAG,mBAAmB,gBACtC;sEACC,KAAK,WAAW,GAAG,OAAO;;;;;;wDAE5B,CAAC,KAAK,WAAW,IAAI,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,GAAG,mBACvE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,6MAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAC1B,eAAe,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;uCA3BtC;;;;;;;;;;;;;;;;kCAuClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,WAAW;;;;;;0CAGxB,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/TaskManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nimport RewriteProgress from './RewriteProgress';\nimport <PERSON>Hist<PERSON> from './JobHistory';\nimport ApiKeyStats from './ApiKeyStats';\nimport { Activity, History, Key, Eye } from 'lucide-react';\n\ninterface TaskManagerProps {\n  currentJobId?: string;\n  onJobComplete?: () => void;\n}\n\nexport default function TaskManager({ currentJobId, onJobComplete }: TaskManagerProps) {\n  const [selectedJobId, setSelectedJobId] = useState<string | null>(currentJobId || null);\n  const [activeTab, setActiveTab] = useState(currentJobId ? 'current' : 'history');\n\n  const handleJobSelect = (jobId: string) => {\n    setSelectedJobId(jobId);\n    setActiveTab('current');\n  };\n\n  const handleJobComplete = () => {\n    if (onJobComplete) {\n      onJobComplete();\n    }\n    // 任务完成后切换到历史页面\n    setTimeout(() => {\n      setActiveTab('history');\n    }, 2000);\n  };\n\n  const tabs = [\n    { id: 'current', label: '当前任务', icon: Activity },\n    { id: 'history', label: '任务历史', icon: History },\n    { id: 'stats', label: 'API状态', icon: Key },\n  ];\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto\">\n      {/* Tab Navigation */}\n      <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ${\n                activeTab === tab.id\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-800'\n              }`}\n            >\n              <Icon className=\"mr-2\" size={16} />\n              {tab.label}\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"mt-6\">\n        {activeTab === 'current' && (\n          <div>\n            {selectedJobId ? (\n              <RewriteProgress\n                jobId={selectedJobId}\n                onComplete={handleJobComplete}\n              />\n            ) : (\n              <div className=\"bg-white rounded-lg shadow-md p-8 text-center\">\n                <Eye className=\"mx-auto mb-4 text-gray-400\" size={48} />\n                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">没有正在进行的任务</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  当前没有正在执行的改写任务。你可以：\n                </p>\n                <div className=\"space-y-2 text-sm text-gray-500\">\n                  <p>• 从任务历史中选择一个任务查看详情</p>\n                  <p>• 创建新的改写任务</p>\n                  <p>• 查看API Key使用状态</p>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'history' && (\n          <JobHistory onJobSelect={handleJobSelect} />\n        )}\n\n        {activeTab === 'stats' && (\n          <ApiKeyStats refreshInterval={5000} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAce,SAAS,YAAY,EAAE,YAAY,EAAE,aAAa,EAAoB;IACnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB,gBAAgB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC,eAAe,YAAY;IAEtE,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB;QACF;QACA,eAAe;QACf,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,sNAAQ;QAAC;QAC/C;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,mNAAO;QAAC;QAC9C;YAAE,IAAI;YAAS,OAAO;YAAS,MAAM,uMAAG;QAAC;KAC1C;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC;oBACT,MAAM,OAAO,IAAI,IAAI;oBACrB,qBACE,8OAAC;wBAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wBAClC,WAAW,CAAC,mGAAmG,EAC7G,cAAc,IAAI,EAAE,GAChB,qCACA,qCACJ;;0CAEF,8OAAC;gCAAK,WAAU;gCAAO,MAAM;;;;;;4BAC5B,IAAI,KAAK;;uBATL,IAAI,EAAE;;;;;gBAYjB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,2BACb,8OAAC;kCACE,8BACC,8OAAC,gJAAe;4BACd,OAAO;4BACP,YAAY;;;;;iDAGd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uMAAG;oCAAC,WAAU;oCAA6B,MAAM;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;oBAOZ,cAAc,2BACb,8OAAC,2IAAU;wBAAC,aAAa;;;;;;oBAG1B,cAAc,yBACb,8OAAC,4IAAW;wBAAC,iBAAiB;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 3829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  onClose: () => void;\n  duration?: number;\n}\n\nexport default function Toast({ message, type, onClose, duration = 3000 }: ToastProps) {\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"text-green-500\" size={20} />;\n      case 'error':\n        return <XCircle className=\"text-red-500\" size={20} />;\n      case 'info':\n        return <AlertCircle className=\"text-blue-500\" size={20} />;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  return (\n    <div className={`fixed top-4 right-4 z-50 max-w-sm w-full ${getBgColor()} border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full duration-300`}>\n      <div className=\"flex items-start space-x-3\">\n        {getIcon()}\n        <div className=\"flex-1 text-sm text-gray-800\">\n          {message}\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-gray-600\"\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYe,SAAS,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,IAAI,EAAc;IACnF,IAAA,kNAAS,EAAC;QACR,IAAI,WAAW,GAAG;YAChB,MAAM,QAAQ,WAAW,SAAS;YAClC,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0OAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,uNAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,8OAAC,mOAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QACxD;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yCAAyC,EAAE,aAAa,iFAAiF,CAAC;kBACzJ,cAAA,8OAAC;YAAI,WAAU;;gBACZ;8BACD,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,iMAAC;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 3939, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport NovelSelector from '@/components/NovelSelector';\nimport ChapterSelector from '@/components/ChapterSelector';\nimport RuleEditor from '@/components/RuleEditor';\nimport CharacterManager from '@/components/CharacterManager';\nimport RewriteProgress from '@/components/RewriteProgress';\nimport TaskManager from '@/components/TaskManager';\nimport Toast from '@/components/Toast';\nimport { Novel, Chapter } from '@/lib/database';\nimport { HelpCircle } from 'lucide-react';\n\ninterface Character {\n  id: string;\n  name: string;\n  type: '男主' | '女主' | '配角' | '反派' | '其他';\n  description: string;\n}\n\ninterface ToastState {\n  show: boolean;\n  message: string;\n  type: 'success' | 'error' | 'info';\n}\n\nexport default function Home() {\n  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);\n  const [selectedChapters, setSelectedChapters] = useState<string>('');\n  const [rewriteRules, setRewriteRules] = useState<string>('');\n  const [characters, setCharacters] = useState<Character[]>([]);\n  const [isRewriting, setIsRewriting] = useState(false);\n  const [currentJobId, setCurrentJobId] = useState<string | null>(null);\n  const [showTaskManager, setShowTaskManager] = useState(false);\n  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info') => {\n    setToast({ show: true, message, type });\n  };\n\n  const hideToast = () => {\n    setToast({ show: false, message: '', type: 'info' });\n  };\n\n  const handleSaveToPreset = async (rules: string) => {\n    const name = prompt('请输入预设名称:');\n    if (!name) return;\n\n    const description = prompt('请输入预设描述 (可选):') || '';\n\n    try {\n      const response = await fetch('/api/presets', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name,\n          description,\n          rules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        showToast('规则已保存到预设', 'success');\n      } else {\n        showToast(`保存失败: ${result.error}`, 'error');\n      }\n    } catch (error) {\n      console.error('保存预设失败:', error);\n      showToast('保存预设失败', 'error');\n    }\n  };\n\n  const handleStartRewrite = async () => {\n    if (!selectedNovel || !selectedChapters || !rewriteRules) {\n      showToast('请完整填写所有信息', 'error');\n      return;\n    }\n\n    setIsRewriting(true);\n\n    try {\n      // 构建包含人物信息的改写规则\n      let enhancedRules = rewriteRules;\n      if (characters.length > 0) {\n        const characterInfo = characters.map(char =>\n          `${char.name}(${char.type}${char.description ? ': ' + char.description : ''})`\n        ).join('、');\n        enhancedRules = `人物设定：${characterInfo}\\n\\n${rewriteRules}`;\n      }\n\n      const response = await fetch('/api/rewrite', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          novelId: selectedNovel.id,\n          chapterRange: selectedChapters,\n          rules: enhancedRules,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setCurrentJobId(result.data.jobId);\n        showToast('改写任务已开始', 'info');\n      } else {\n        showToast(`改写失败: ${result.error}`, 'error');\n        setIsRewriting(false);\n      }\n    } catch (error) {\n      console.error('改写请求失败:', error);\n      showToast('改写请求失败', 'error');\n      setIsRewriting(false);\n    }\n  };\n\n  const handleRewriteComplete = () => {\n    setIsRewriting(false);\n    setCurrentJobId(null);\n    showToast('改写完成！', 'success');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            小说改写工具\n          </h1>\n          <div className=\"flex items-center space-x-4\">\n            {/* 任务管理按钮 */}\n            <button\n              onClick={() => setShowTaskManager(!showTaskManager)}\n              className=\"flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <svg className=\"mr-1\" width={18} height={18} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>\n                <path d=\"M9 9h6v6H9z\"/>\n              </svg>\n              任务管理\n            </button>\n            {/* 开始改写按钮 */}\n            <button\n              onClick={handleStartRewrite}\n              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}\n              className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors\"\n            >\n              {isRewriting ? '改写中...' : '开始改写'}\n            </button>\n            <Link\n              href=\"/help\"\n              className=\"flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <HelpCircle className=\"mr-1\" size={18} />\n              帮助\n            </Link>\n          </div>\n        </div>\n\n        {/* 任务管理器 */}\n        {showTaskManager && (\n          <div className=\"mb-6\">\n            <TaskManager\n              currentJobId={currentJobId}\n              onJobComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {/* 进度显示 */}\n        {isRewriting && currentJobId && !showTaskManager && (\n          <div className=\"mb-4\">\n            <RewriteProgress\n              jobId={currentJobId}\n              onComplete={handleRewriteComplete}\n            />\n          </div>\n        )}\n\n        {!showTaskManager && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* 左侧：改写规则 */}\n          <div className=\"lg:col-span-1\">\n            <RuleEditor\n              rules={rewriteRules}\n              onRulesChange={setRewriteRules}\n              onSaveToPreset={handleSaveToPreset}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 中间：小说选择和人物管理 */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            <NovelSelector\n              selectedNovel={selectedNovel}\n              onNovelSelect={setSelectedNovel}\n              disabled={isRewriting}\n            />\n            <CharacterManager\n              characters={characters}\n              onCharactersChange={setCharacters}\n              disabled={isRewriting}\n            />\n          </div>\n\n          {/* 右侧：章节选择 */}\n          <div className=\"lg:col-span-1\">\n            <ChapterSelector\n              novel={selectedNovel}\n              selectedChapters={selectedChapters}\n              onChaptersChange={setSelectedChapters}\n              disabled={isRewriting}\n            />\n          </div>\n        </div>\n        )}\n      </div>\n\n      {/* Toast 通知 */}\n      {toast.show && (\n        <Toast\n          message={toast.message}\n          type={toast.type}\n          onClose={hideToast}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAe;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAS;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAS;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAc,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAC;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAa;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAAO;IAExF,MAAM,YAAY,CAAC,SAAiB;QAClC,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;IACvC;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;YAAO,SAAS;YAAI,MAAM;QAAO;IACpD;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,OAAO;QACpB,IAAI,CAAC,MAAM;QAEX,MAAM,cAAc,OAAO,oBAAoB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,YAAY;YACxB,OAAO;gBACL,UAAU,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE,EAAE;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;QACtB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,cAAc;YACxD,UAAU,aAAa;YACvB;QACF;QAEA,eAAe;QAEf,IAAI;YACF,gBAAgB;YAChB,IAAI,gBAAgB;YACpB,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA,OACnC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,GAAG,CAAC,CAAC,EAC9E,IAAI,CAAC;gBACP,gBAAgB,CAAC,KAAK,EAAE,cAAc,IAAI,EAAE,cAAc;YAC5D;YAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,cAAc,EAAE;oBACzB,cAAc;oBACd,OAAO;gBACT;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,gBAAgB,OAAO,IAAI,CAAC,KAAK;gBACjC,UAAU,WAAW;YACvB,OAAO;gBACL,UAAU,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE,EAAE;gBACnC,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU,UAAU;YACpB,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,UAAU,SAAS;IACrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,SAAS,IAAM,mBAAmB,CAAC;wCACnC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAO,OAAO;gDAAI,QAAQ;gDAAI,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;;kEAC7G,8OAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEACnD,8OAAC;wDAAK,GAAE;;;;;;;;;;;;4CACJ;;;;;;;kDAIR,8OAAC;wCACC,SAAS;wCACT,UAAU,eAAe,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;wCACjE,WAAU;kDAET,cAAc,WAAW;;;;;;kDAE5B,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,4OAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;oBAO9C,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAW;4BACV,cAAc;4BACd,eAAe;;;;;;;;;;;oBAMpB,eAAe,gBAAgB,CAAC,iCAC/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gJAAe;4BACd,OAAO;4BACP,YAAY;;;;;;;;;;;oBAKjB,CAAC,iCACA,8OAAC;wBAAI,WAAU;;0CAEf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2IAAU;oCACT,OAAO;oCACP,eAAe;oCACf,gBAAgB;oCAChB,UAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAa;wCACZ,eAAe;wCACf,eAAe;wCACf,UAAU;;;;;;kDAEZ,8OAAC,iJAAgB;wCACf,YAAY;wCACZ,oBAAoB;wCACpB,UAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gJAAe;oCACd,OAAO;oCACP,kBAAkB;oCAClB,kBAAkB;oCAClB,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAQjB,MAAM,IAAI,kBACT,8OAAC,sIAAK;gBACJ,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS;;;;;;;;;;;;AAKnB", "debugId": null}}]}