import fs from 'fs';
import path from 'path';
import { novelDb, chapterDb, Novel, Chapter } from './database';
import { fileManager } from './file-manager';

// 小说解析配置
interface ParseConfig {
  chapterPattern: RegExp;
  minChapterLength: number;
  maxChapterLength: number;
}

// 默认章节匹配模式
const DEFAULT_CHAPTER_PATTERNS = [
  // 一个正则表达式处理多种情况
  /^\s*(?:第[一二三四五六七八九十百千万\d]+[章节卷回集]|Chapter\s+\d+)\s*.*$/gmi
];

// 解析小说文件
export async function parseNovelFile(filePath: string): Promise<{
  novel: Novel;
  chapters: Chapter[];
}> {
  const filename = path.basename(filePath);
  const title = filename.replace(/\.(txt|md)$/i, '');

  // 读取文件内容
  const content = fs.readFileSync(filePath, 'utf-8');

  // 创建小说记录
  const novel = novelDb.create({
    title,
    filename,
  });

  // 解析章节
  const chapters = parseChapters(content, novel.id);

  // 批量创建章节记录
  const createdChapters = chapterDb.createBatch(chapters);

  // 更新小说的章节数量
  novelDb.update(novel.id, { chapterCount: createdChapters.length });

  // 保存章节文件
  await saveChapterFiles(createdChapters);

  return {
    novel: { ...novel, chapterCount: createdChapters.length },
    chapters: createdChapters,
  };
}

// 解析章节内容
function parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {
  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];

  // 尝试不同的章节匹配模式
  let bestPattern: RegExp | null = null;
  let bestMatches: RegExpMatchArray[] = [];

  for (const pattern of DEFAULT_CHAPTER_PATTERNS) {
    const matches = Array.from(content.matchAll(pattern));
    console.log(`Pattern ${pattern.source} found ${matches.length} matches`);
    if (matches.length > bestMatches.length) {
      bestPattern = pattern;
      bestMatches = matches;
    }
  }

  console.log(`Best pattern found ${bestMatches.length} chapters`);

  if (!bestPattern || bestMatches.length === 0) {
    // 如果没有找到章节标记，将整个文件作为一章
    chapters.push({
      novelId,
      chapterNumber: 1,
      title: '全文',
      content: content.trim(),
      filename: `chapter_1.txt`,
    });
    return chapters;
  }

  // 根据匹配结果分割章节
  const chapterPositions = bestMatches.map((match, index) => ({
    index: match.index!,
    title: extractChapterTitle(match[0]),
    chapterNumber: index + 1,
  }));

  for (let i = 0; i < chapterPositions.length; i++) {
    const currentPos = chapterPositions[i];
    const nextPos = chapterPositions[i + 1];

    const startIndex = currentPos.index;
    const endIndex = nextPos ? nextPos.index : content.length;

    const chapterContent = content.slice(startIndex, endIndex).trim();

    if (chapterContent.length > 50) { // 过滤太短的章节
      chapters.push({
        novelId,
        chapterNumber: currentPos.chapterNumber,
        title: currentPos.title || `第${currentPos.chapterNumber}章`,
        content: chapterContent,
        filename: `chapter_${currentPos.chapterNumber}.txt`,
      });
    }
  }

  console.log(`Successfully parsed ${chapters.length} chapters`);
  return chapters;
}

// 提取章节标题
function extractChapterTitle(chapterText: string): string {
  const lines = chapterText.split('\n');
  const firstLine = lines[0].trim();

  // 如果第一行看起来像标题，使用它
  if (firstLine.length < 100 && firstLine.length > 0) {
    return firstLine;
  }

  // 否则尝试从前几行中找到标题
  for (let i = 0; i < Math.min(3, lines.length); i++) {
    const line = lines[i].trim();
    if (line.length > 0 && line.length < 100) {
      return line;
    }
  }

  return '未命名章节';
}

// 保存章节文件到chapters目录
async function saveChapterFiles(chapters: Chapter[]): Promise<void> {
  // 为每个小说创建子目录
  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];

  for (const novelId of novelIds) {
    const novel = novelDb.getById(novelId);
    if (!novel) continue;

    const novelDir = fileManager.getNovelChaptersDir(novel.title);

    // 保存该小说的所有章节
    const novelChapters = chapters.filter(ch => ch.novelId === novelId);
    for (const chapter of novelChapters) {
      const chapterPath = path.join(novelDir, chapter.filename);
      fileManager.writeFile(chapterPath, chapter.content);
    }
  }
}

// 获取novels目录中的所有小说文件
export function getAvailableNovels(): string[] {
  const novelsDir = fileManager.getNovelsDir();
  return fileManager.listFiles(novelsDir, ['.txt', '.md']);
}

// 检查小说是否已经被解析
export function isNovelParsed(filename: string): boolean {
  const novels = novelDb.getAll();
  return novels.some(novel => novel.filename === filename);
}

// 重新解析小说（删除旧数据并重新解析）
export async function reparseNovel(filename: string): Promise<{
  novel: Novel;
  chapters: Chapter[];
} | null> {
  const novelsDir = fileManager.getNovelsDir();
  const filePath = path.join(novelsDir, filename);

  if (!fileManager.fileExists(filePath)) {
    return null;
  }

  // 删除旧的小说和章节数据
  const existingNovels = novelDb.getAll();
  const existingNovel = existingNovels.find(novel => novel.filename === filename);

  if (existingNovel) {
    chapterDb.deleteByNovelId(existingNovel.id);
    novelDb.delete(existingNovel.id);
  }

  // 重新解析
  return await parseNovelFile(filePath);
}

// 解析章节范围字符串 (例如: "1-5,7,10-12")
export function parseChapterRange(rangeStr: string, maxChapter: number): number[] {
  const chapters: number[] = [];
  const parts = rangeStr.split(',').map(part => part.trim());

  for (const part of parts) {
    if (part.includes('-')) {
      // 范围格式 (例如: "1-5")
      const [start, end] = part.split('-').map(num => parseInt(num.trim()));
      if (!isNaN(start) && !isNaN(end) && start <= end) {
        for (let i = start; i <= Math.min(end, maxChapter); i++) {
          if (i > 0 && !chapters.includes(i)) {
            chapters.push(i);
          }
        }
      }
    } else {
      // 单个章节
      const chapterNum = parseInt(part);
      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
        chapters.push(chapterNum);
      }
    }
  }

  return chapters.sort((a, b) => a - b);
}
