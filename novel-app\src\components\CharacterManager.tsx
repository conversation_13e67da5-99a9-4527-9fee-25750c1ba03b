'use client';

import { useState } from 'react';
import { Users, Plus, X } from 'lucide-react';

interface Character {
  id: string;
  name: string;
  type: '男主' | '女主' | '配角' | '反派' | '其他';
  description: string;
}

interface CharacterManagerProps {
  characters: Character[];
  onCharactersChange: (characters: Character[]) => void;
  disabled?: boolean;
}

export default function CharacterManager({ characters, onCharactersChange, disabled }: CharacterManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCharacter, setNewCharacter] = useState<Omit<Character, 'id'>>({
    name: '',
    type: '其他',
    description: ''
  });

  const handleAddCharacter = () => {
    if (!newCharacter.name.trim()) return;
    
    const character: Character = {
      id: Date.now().toString(),
      ...newCharacter
    };
    
    onCharactersChange([...characters, character]);
    setNewCharacter({ name: '', type: '其他', description: '' });
    setShowAddForm(false);
  };

  const handleRemoveCharacter = (id: string) => {
    onCharactersChange(characters.filter(char => char.id !== id));
  };

  const characterTypes: Character['type'][] = ['男主', '女主', '配角', '反派', '其他'];

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <Users className="mr-2" size={18} />
          人物设定
        </h2>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          disabled={disabled}
          className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
          title="添加人物"
        >
          <Plus size={16} />
        </button>
      </div>

      {/* 添加人物表单 */}
      {showAddForm && (
        <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="space-y-2">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="人物名称"
                value={newCharacter.name}
                onChange={(e) => setNewCharacter({ ...newCharacter, name: e.target.value })}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <select
                value={newCharacter.type}
                onChange={(e) => setNewCharacter({ ...newCharacter, type: e.target.value as Character['type'] })}
                className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                {characterTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            <input
              type="text"
              placeholder="备注描述"
              value={newCharacter.description}
              onChange={(e) => setNewCharacter({ ...newCharacter, description: e.target.value })}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <div className="flex space-x-2">
              <button
                onClick={handleAddCharacter}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                添加
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 人物列表 */}
      <div className="space-y-2">
        {characters.length === 0 ? (
          <div className="text-center py-4 text-gray-500 text-sm">
            暂无人物设定
          </div>
        ) : (
          characters.map((character) => (
            <div key={character.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-800 text-sm">{character.name}</span>
                  <span className={`px-2 py-0.5 text-xs rounded ${
                    character.type === '男主' ? 'bg-blue-100 text-blue-800' :
                    character.type === '女主' ? 'bg-pink-100 text-pink-800' :
                    character.type === '配角' ? 'bg-green-100 text-green-800' :
                    character.type === '反派' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {character.type}
                  </span>
                </div>
                {character.description && (
                  <div className="text-xs text-gray-600 mt-1 truncate">
                    {character.description}
                  </div>
                )}
              </div>
              <button
                onClick={() => handleRemoveCharacter(character.id)}
                disabled={disabled}
                className="p-1 text-gray-400 hover:text-red-600 disabled:opacity-50"
                title="删除"
              >
                <X size={14} />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
