import { NextResponse } from 'next/server';
import { resetApiKeyStats } from '@/lib/gemini';

// POST - 重置API Key统计
export async function POST() {
  try {
    resetApiKeyStats();
    
    return NextResponse.json({
      success: true,
      message: 'API Key统计已重置',
    });
  } catch (error) {
    console.error('重置API统计失败:', error);
    return NextResponse.json(
      { success: false, error: '重置API统计失败' },
      { status: 500 }
    );
  }
}
