# Gemini API 改进说明

## 概述

本次改进针对你提到的问题进行了全面的优化，包括多API Key管理、并发控制、实时写入、详细进度显示和任务记录功能。

## 主要改进

### 1. 多API Key池管理

**问题**: 之前只使用单个API Key，容易遇到429限流错误
**解决方案**: 
- 实现了智能的API Key池管理系统
- 支持5个API Key，第一个key权重为4倍，其他为1倍
- 自动根据权重和使用频率选择最佳可用的key
- 429错误后自动切换到其他key，并设置冷却时间

**配置**:
```typescript
const API_KEYS = [
  { key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', name: 'My First Project', weight: 4 },
  { key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', name: 'ankibot', weight: 1 },
  { key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', name: 'Generative Language Client', weight: 1 },
  { key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', name: 'In The Novel', weight: 1 },
  { key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', name: 'chat', weight: 1 }
];
```

### 2. 改进的并发控制

**问题**: 之前并发数过高(10)，容易触发限流
**解决方案**:
- 降低并发数到3，更保守的策略
- 实现信号量(Semaphore)控制并发
- 增加请求间隔(1秒)
- 自动重试机制(最多3次)

### 3. 实时写入功能

**问题**: 之前是一次性写入，已完成的内容可能丢失
**解决方案**:
- 每个章节完成后立即写入文件
- 实时更新数据库中的章节结果
- 支持章节完成回调，实时反馈进度

### 4. 详细进度显示

**问题**: 之前只有简单的进度条，看不到详细信息
**解决方案**:
- 显示章节统计(总数、已完成、失败、剩余)
- 显示性能统计(总耗时、平均每章时间、Token消耗)
- 显示API Key使用状态和冷却时间
- 显示最近完成的章节详情
- 实时更新所有统计信息

### 5. 任务记录和历史

**问题**: 没有任务历史记录功能
**解决方案**:
- 扩展数据库模型，记录详细的任务信息
- 创建任务历史查看组件
- 支持查看历史任务详情
- 支持删除任务记录

### 6. API Key状态监控

**新功能**:
- 实时显示所有API Key的使用状态
- 显示请求次数、权重、可用性
- 显示冷却剩余时间
- 支持连接测试和统计重置

## 新增组件

### 1. TaskManager (任务管理器)
- 集成当前任务、历史任务和API状态
- 支持标签页切换
- 统一的任务管理界面

### 2. JobHistory (任务历史)
- 显示所有历史任务
- 支持查看任务详情
- 支持删除任务记录
- 显示任务统计信息

### 3. ApiKeyStats (API状态)
- 实时显示API Key状态
- 支持连接测试
- 支持统计重置
- 自动刷新功能

## 新增API路由

### 1. `/api/gemini/stats` - 获取API统计
### 2. `/api/gemini/test` - 测试API连接
### 3. `/api/gemini/reset` - 重置API统计
### 4. `/api/jobs` - 支持DELETE方法删除任务

## 数据库改进

扩展了`RewriteJob`接口，增加了详细信息字段：
- 章节统计信息
- 性能统计信息
- API Key使用统计
- 章节处理结果详情
- 模型和并发配置信息

## 使用方法

### 1. 基本使用
在主页面点击"任务管理"按钮，可以查看：
- 当前正在进行的任务
- 历史任务记录
- API Key使用状态

### 2. 监控API状态
在API状态页面可以：
- 查看所有API Key的实时状态
- 测试API连接
- 重置使用统计
- 监控冷却时间

### 3. 查看任务历史
在任务历史页面可以：
- 查看所有历史任务
- 点击查看任务详情
- 删除不需要的任务记录

## 技术特性

### 1. 智能负载均衡
- 根据API Key权重和使用频率自动选择
- 避免过度使用单个key
- 自动处理限流和错误

### 2. 容错机制
- 自动重试失败的请求
- 429错误后自动切换key
- 网络错误的优雅处理

### 3. 实时反馈
- 章节完成后立即写入
- 实时更新进度和统计
- 详细的错误信息显示

### 4. 性能优化
- 降低并发数避免限流
- 增加请求间隔
- 智能的key选择算法

## 配置说明

### 1. API Key配置
在`novel-app/src/lib/gemini.ts`中的`API_KEYS`数组中配置你的API Key。

### 2. 并发控制
- `concurrency`: 并发数量(默认3)
- `REQUEST_DELAY`: 请求间隔(默认1000ms)
- `COOLDOWN_DURATION`: 冷却时间(默认60000ms)
- `MAX_RETRIES`: 最大重试次数(默认3)

### 3. 刷新间隔
- API状态页面默认5秒刷新一次
- 任务进度默认2秒检查一次

## 注意事项

1. 确保所有API Key都是有效的
2. 根据你的API配额调整并发数和请求间隔
3. 定期查看API使用统计，避免超出限额
4. 及时清理不需要的历史任务记录

## 故障排除

### 1. 如果遇到429错误
- 检查API Key是否有效
- 降低并发数
- 增加请求间隔
- 查看API Key冷却状态

### 2. 如果任务失败
- 查看任务详情中的错误信息
- 检查网络连接
- 验证API Key权限
- 重试任务

### 3. 如果进度不更新
- 刷新页面
- 检查网络连接
- 查看浏览器控制台错误
