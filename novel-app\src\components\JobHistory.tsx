'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, AlertCircle, Eye, Trash2, RefreshCw } from 'lucide-react';

interface JobHistoryProps {
  onJobSelect?: (jobId: string) => void;
}

interface JobSummary {
  id: string;
  novelId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: string;
  createdAt: string;
  updatedAt: string;
  details?: {
    totalChapters: number;
    completedChapters: number;
    failedChapters: number;
    totalTokensUsed: number;
    totalProcessingTime: number;
    model?: string;
  };
}

export default function JobHistory({ onJobSelect }: JobHistoryProps) {
  const [jobs, setJobs] = useState<JobSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [novels, setNovels] = useState<Record<string, string>>({});

  useEffect(() => {
    loadJobs();
    loadNovels();
  }, []);

  const loadJobs = async () => {
    try {
      const response = await fetch('/api/jobs');
      const result = await response.json();
      
      if (result.success) {
        // 按创建时间倒序排列
        const sortedJobs = result.data.sort((a: JobSummary, b: JobSummary) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        setJobs(sortedJobs);
      }
    } catch (error) {
      console.error('加载任务历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadNovels = async () => {
    try {
      const response = await fetch('/api/novels');
      const result = await response.json();
      
      if (result.success) {
        const novelMap: Record<string, string> = {};
        result.data.forEach((novel: any) => {
          novelMap[novel.id] = novel.title;
        });
        setNovels(novelMap);
      }
    } catch (error) {
      console.error('加载小说列表失败:', error);
    }
  };

  const deleteJob = async (jobId: string) => {
    if (!confirm('确定要删除这个任务记录吗？')) return;
    
    try {
      const response = await fetch(`/api/jobs?jobId=${jobId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setJobs(jobs.filter(job => job.id !== jobId));
      }
    } catch (error) {
      console.error('删除任务失败:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-500" size={16} />;
      case 'processing':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>;
      case 'completed':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'failed':
        return <XCircle className="text-red-500" size={16} />;
      default:
        return <AlertCircle className="text-gray-500" size={16} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-50 border-yellow-200';
      case 'processing': return 'bg-blue-50 border-blue-200';
      case 'completed': return 'bg-green-50 border-green-200';
      case 'failed': return 'bg-red-50 border-red-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.round(ms / 1000);
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.round(seconds / 60);
    if (minutes < 60) return `${minutes}分钟`;
    const hours = Math.round(minutes / 60);
    return `${hours}小时`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">加载任务历史中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">任务历史</h3>
          <button
            onClick={loadJobs}
            className="flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            <RefreshCw size={14} className="mr-1" />
            刷新
          </button>
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto">
        {jobs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无任务记录
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {jobs.map((job) => (
              <div key={job.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      {getStatusIcon(job.status)}
                      <span className="ml-2 font-medium text-gray-800">
                        {novels[job.novelId] || '未知小说'}
                      </span>
                      <span className={`ml-2 px-2 py-1 text-xs rounded border ${getStatusColor(job.status)}`}>
                        {getStatusText(job.status)}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 space-y-1">
                      <div>创建时间: {new Date(job.createdAt).toLocaleString()}</div>
                      {job.details && (
                        <div className="flex space-x-4">
                          <span>章节: {job.details.completedChapters}/{job.details.totalChapters}</span>
                          {job.details.totalTokensUsed > 0 && (
                            <span>Token: {job.details.totalTokensUsed.toLocaleString()}</span>
                          )}
                          {job.details.totalProcessingTime > 0 && (
                            <span>耗时: {formatDuration(job.details.totalProcessingTime)}</span>
                          )}
                          {job.details.model && (
                            <span>模型: {job.details.model}</span>
                          )}
                        </div>
                      )}
                      {job.status !== 'completed' && job.status !== 'failed' && (
                        <div>进度: {job.progress}%</div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {onJobSelect && (
                      <button
                        onClick={() => onJobSelect(job.id)}
                        className="p-1 text-blue-600 hover:text-blue-800"
                        title="查看详情"
                      >
                        <Eye size={16} />
                      </button>
                    )}
                    <button
                      onClick={() => deleteJob(job.id)}
                      className="p-1 text-red-600 hover:text-red-800"
                      title="删除记录"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
