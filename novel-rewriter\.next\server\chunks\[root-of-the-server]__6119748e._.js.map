{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-rewriter/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: 'male_lead' | 'female_lead' | 'supporting' | 'villain' | 'other';\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteHistory {\n  id: string;\n  novelId: string;\n  jobId: string;\n  ruleId: string;\n  ruleName: string;\n  ruleContent: string;\n  chapters: number[];\n  originalContent: string;\n  rewrittenContent: string;\n  status: 'success' | 'failed';\n  error?: string;\n  createdAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst REWRITE_HISTORY_FILE = path.join(DATA_DIR, 'rewrite_history.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const newNovel: Novel = {\n      ...novel,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 人物相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  getByRole: (novelId: string, role: Character['role']): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId && character.role === role);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 改写历史相关操作\nexport const rewriteHistoryDb = {\n  getAll: (): RewriteHistory[] => readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE),\n\n  getByNovelId: (novelId: string): RewriteHistory[] => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.filter(history => history.novelId === novelId).sort((a, b) =>\n      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n  },\n\n  getById: (id: string): RewriteHistory | undefined => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.find(history => history.id === id);\n  },\n\n  getByJobId: (jobId: string): RewriteHistory[] => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.filter(history => history.jobId === jobId);\n  },\n\n  create: (history: Omit<RewriteHistory, 'id' | 'createdAt'>): RewriteHistory => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const newHistory: RewriteHistory = {\n      ...history,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    histories.push(newHistory);\n    writeJsonFile(REWRITE_HISTORY_FILE, histories);\n    return newHistory;\n  },\n\n  delete: (id: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const index = histories.findIndex(history => history.id === id);\n    if (index === -1) return false;\n\n    histories.splice(index, 1);\n    writeJsonFile(REWRITE_HISTORY_FILE, histories);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const filteredHistories = histories.filter(history => history.novelId !== novelId);\n    writeJsonFile(REWRITE_HISTORY_FILE, filteredHistories);\n    return true;\n  },\n\n  deleteByJobId: (jobId: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const filteredHistories = histories.filter(history => history.jobId !== jobId);\n    writeJsonFile(REWRITE_HISTORY_FILE, filteredHistories);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAsEA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,kBAAkB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,uBAAuB,4GAAI,CAAC,IAAI,CAAC,UAAU;AACjD,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AAEtC,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,WAAW,CAAC,SAAiB;QAC3B,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK,WAAW,UAAU,IAAI,KAAK;IAC5F;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,IAAwB,aAA6B;IAE7D,cAAc,CAAC;QACb,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK,SAAS,IAAI,CAAC,CAAC,GAAG,IACvE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IAEnE;IAEA,SAAS,CAAC;QACR,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAClD;IAEA,YAAY,CAAC;QACX,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,YAAY,aAA6B;QAC/C,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,UAAU,IAAI,CAAC;QACf,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,YAAY,aAA6B;QAC/C,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC5D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,MAAM,CAAC,OAAO;QACxB,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,YAAY,aAA6B;QAC/C,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QAC1E,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,eAAe,CAAC;QACd,MAAM,YAAY,aAA6B;QAC/C,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;QACxE,cAAc,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-rewriter/src/app/api/characters/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { characterDb } from '@/lib/database';\n\n// GET - 获取人物列表\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const novelId = searchParams.get('novelId');\n    const role = searchParams.get('role') as any;\n    \n    if (novelId) {\n      if (role) {\n        // 获取特定小说的特定角色类型的人物\n        const characters = characterDb.getByRole(novelId, role);\n        return NextResponse.json({\n          success: true,\n          data: characters,\n        });\n      } else {\n        // 获取特定小说的所有人物\n        const characters = characterDb.getByNovelId(novelId);\n        return NextResponse.json({\n          success: true,\n          data: characters,\n        });\n      }\n    } else {\n      // 获取所有人物\n      const characters = characterDb.getAll();\n      return NextResponse.json({\n        success: true,\n        data: characters,\n      });\n    }\n  } catch (error) {\n    console.error('获取人物列表失败:', error);\n    return NextResponse.json(\n      { success: false, error: '获取人物列表失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - 创建新人物\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { novelId, name, role, description, personality, appearance, relationships } = body;\n\n    // 验证必填字段\n    if (!novelId || !name || !role || !description) {\n      return NextResponse.json(\n        { success: false, error: '小说ID、人物名称、角色类型和描述不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 验证角色类型\n    const validRoles = ['male_lead', 'female_lead', 'supporting', 'villain', 'other'];\n    if (!validRoles.includes(role)) {\n      return NextResponse.json(\n        { success: false, error: '无效的角色类型' },\n        { status: 400 }\n      );\n    }\n\n    // 检查是否已存在同名人物\n    const existingCharacters = characterDb.getByNovelId(novelId);\n    const duplicateName = existingCharacters.find(char => char.name === name);\n    if (duplicateName) {\n      return NextResponse.json(\n        { success: false, error: '该小说中已存在同名人物' },\n        { status: 400 }\n      );\n    }\n\n    // 检查主角角色是否已存在\n    if (role === 'male_lead' || role === 'female_lead') {\n      const existingLeads = characterDb.getByRole(novelId, role);\n      if (existingLeads.length > 0) {\n        const roleDisplayName = role === 'male_lead' ? '男主角' : '女主角';\n        return NextResponse.json(\n          { success: false, error: `该小说已存在${roleDisplayName}` },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 创建人物\n    const character = characterDb.create({\n      novelId,\n      name,\n      role,\n      description,\n      personality: personality || '',\n      appearance: appearance || '',\n      relationships: relationships || '',\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: character,\n      message: '人物创建成功',\n    });\n\n  } catch (error) {\n    console.error('创建人物失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建人物失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT - 更新人物信息\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { id, name, role, description, personality, appearance, relationships } = body;\n\n    if (!id) {\n      return NextResponse.json(\n        { success: false, error: '人物ID不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 获取现有人物信息\n    const existingCharacter = characterDb.getById(id);\n    if (!existingCharacter) {\n      return NextResponse.json(\n        { success: false, error: '人物不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 如果更新了名称，检查是否与其他人物重名\n    if (name && name !== existingCharacter.name) {\n      const existingCharacters = characterDb.getByNovelId(existingCharacter.novelId);\n      const duplicateName = existingCharacters.find(char => char.id !== id && char.name === name);\n      if (duplicateName) {\n        return NextResponse.json(\n          { success: false, error: '该小说中已存在同名人物' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 如果更新了角色类型，检查主角角色是否已存在\n    if (role && role !== existingCharacter.role && (role === 'male_lead' || role === 'female_lead')) {\n      const existingLeads = characterDb.getByRole(existingCharacter.novelId, role);\n      if (existingLeads.length > 0) {\n        const roleDisplayName = role === 'male_lead' ? '男主角' : '女主角';\n        return NextResponse.json(\n          { success: false, error: `该小说已存在${roleDisplayName}` },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 更新人物信息\n    const updates: any = {};\n    if (name !== undefined) updates.name = name;\n    if (role !== undefined) updates.role = role;\n    if (description !== undefined) updates.description = description;\n    if (personality !== undefined) updates.personality = personality;\n    if (appearance !== undefined) updates.appearance = appearance;\n    if (relationships !== undefined) updates.relationships = relationships;\n\n    const updatedCharacter = characterDb.update(id, updates);\n\n    if (!updatedCharacter) {\n      return NextResponse.json(\n        { success: false, error: '更新人物失败' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: updatedCharacter,\n      message: '人物信息更新成功',\n    });\n\n  } catch (error) {\n    console.error('更新人物失败:', error);\n    return NextResponse.json(\n      { success: false, error: '更新人物失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE - 删除人物\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const id = searchParams.get('id');\n\n    if (!id) {\n      return NextResponse.json(\n        { success: false, error: '人物ID不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 检查人物是否存在\n    const character = characterDb.getById(id);\n    if (!character) {\n      return NextResponse.json(\n        { success: false, error: '人物不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 删除人物\n    const success = characterDb.delete(id);\n\n    if (!success) {\n      return NextResponse.json(\n        { success: false, error: '删除人物失败' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: '人物删除成功',\n    });\n\n  } catch (error) {\n    console.error('删除人物失败:', error);\n    return NextResponse.json(\n      { success: false, error: '删除人物失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,IAAI,SAAS;YACX,IAAI,MAAM;gBACR,mBAAmB;gBACnB,MAAM,aAAa,uIAAW,CAAC,SAAS,CAAC,SAAS;gBAClD,OAAO,gJAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YACF,OAAO;gBACL,cAAc;gBACd,MAAM,aAAa,uIAAW,CAAC,YAAY,CAAC;gBAC5C,OAAO,gJAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YACF;QACF,OAAO;YACL,SAAS;YACT,MAAM,aAAa,uIAAW,CAAC,MAAM;YACrC,OAAO,gJAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;QAErF,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa;YAC9C,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAwB,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,aAAa;YAAC;YAAa;YAAe;YAAc;YAAW;SAAQ;QACjF,IAAI,CAAC,WAAW,QAAQ,CAAC,OAAO;YAC9B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,qBAAqB,uIAAW,CAAC,YAAY,CAAC;QACpD,MAAM,gBAAgB,mBAAmB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACpE,IAAI,eAAe;YACjB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAc,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,IAAI,SAAS,eAAe,SAAS,eAAe;YAClD,MAAM,gBAAgB,uIAAW,CAAC,SAAS,CAAC,SAAS;YACrD,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,kBAAkB,SAAS,cAAc,QAAQ;gBACvD,OAAO,gJAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO,CAAC,MAAM,EAAE,iBAAiB;gBAAC,GACpD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO;QACP,MAAM,YAAY,uIAAW,CAAC,MAAM,CAAC;YACnC;YACA;YACA;YACA;YACA,aAAa,eAAe;YAC5B,YAAY,cAAc;YAC1B,eAAe,iBAAiB;QAClC;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;QAEhF,IAAI,CAAC,IAAI;YACP,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,oBAAoB,uIAAW,CAAC,OAAO,CAAC;QAC9C,IAAI,CAAC,mBAAmB;YACtB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI,QAAQ,SAAS,kBAAkB,IAAI,EAAE;YAC3C,MAAM,qBAAqB,uIAAW,CAAC,YAAY,CAAC,kBAAkB,OAAO;YAC7E,MAAM,gBAAgB,mBAAmB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,KAAK,IAAI,KAAK;YACtF,IAAI,eAAe;gBACjB,OAAO,gJAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAc,GACvC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,wBAAwB;QACxB,IAAI,QAAQ,SAAS,kBAAkB,IAAI,IAAI,CAAC,SAAS,eAAe,SAAS,aAAa,GAAG;YAC/F,MAAM,gBAAgB,uIAAW,CAAC,SAAS,CAAC,kBAAkB,OAAO,EAAE;YACvE,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,kBAAkB,SAAS,cAAc,QAAQ;gBACvD,OAAO,gJAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO,CAAC,MAAM,EAAE,iBAAiB;gBAAC,GACpD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,SAAS;QACT,MAAM,UAAe,CAAC;QACtB,IAAI,SAAS,WAAW,QAAQ,IAAI,GAAG;QACvC,IAAI,SAAS,WAAW,QAAQ,IAAI,GAAG;QACvC,IAAI,gBAAgB,WAAW,QAAQ,WAAW,GAAG;QACrD,IAAI,gBAAgB,WAAW,QAAQ,WAAW,GAAG;QACrD,IAAI,eAAe,WAAW,QAAQ,UAAU,GAAG;QACnD,IAAI,kBAAkB,WAAW,QAAQ,aAAa,GAAG;QAEzD,MAAM,mBAAmB,uIAAW,CAAC,MAAM,CAAC,IAAI;QAEhD,IAAI,CAAC,kBAAkB;YACrB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,YAAY,uIAAW,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,WAAW;YACd,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,UAAU,uIAAW,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,SAAS;YACZ,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}