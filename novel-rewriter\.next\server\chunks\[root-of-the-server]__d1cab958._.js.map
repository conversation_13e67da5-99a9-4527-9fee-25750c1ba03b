{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-rewriter/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: 'male_lead' | 'female_lead' | 'supporting' | 'villain' | 'other';\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteHistory {\n  id: string;\n  novelId: string;\n  jobId: string;\n  ruleId: string;\n  ruleName: string;\n  ruleContent: string;\n  chapters: number[];\n  originalContent: string;\n  rewrittenContent: string;\n  status: 'success' | 'failed';\n  error?: string;\n  createdAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst REWRITE_HISTORY_FILE = path.join(DATA_DIR, 'rewrite_history.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const newNovel: Novel = {\n      ...novel,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 人物相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  getByRole: (novelId: string, role: Character['role']): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId && character.role === role);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 改写历史相关操作\nexport const rewriteHistoryDb = {\n  getAll: (): RewriteHistory[] => readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE),\n\n  getByNovelId: (novelId: string): RewriteHistory[] => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.filter(history => history.novelId === novelId).sort((a, b) =>\n      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n  },\n\n  getById: (id: string): RewriteHistory | undefined => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.find(history => history.id === id);\n  },\n\n  getByJobId: (jobId: string): RewriteHistory[] => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    return histories.filter(history => history.jobId === jobId);\n  },\n\n  create: (history: Omit<RewriteHistory, 'id' | 'createdAt'>): RewriteHistory => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const newHistory: RewriteHistory = {\n      ...history,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    histories.push(newHistory);\n    writeJsonFile(REWRITE_HISTORY_FILE, histories);\n    return newHistory;\n  },\n\n  delete: (id: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const index = histories.findIndex(history => history.id === id);\n    if (index === -1) return false;\n\n    histories.splice(index, 1);\n    writeJsonFile(REWRITE_HISTORY_FILE, histories);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const filteredHistories = histories.filter(history => history.novelId !== novelId);\n    writeJsonFile(REWRITE_HISTORY_FILE, filteredHistories);\n    return true;\n  },\n\n  deleteByJobId: (jobId: string): boolean => {\n    const histories = readJsonFile<RewriteHistory>(REWRITE_HISTORY_FILE);\n    const filteredHistories = histories.filter(history => history.jobId !== jobId);\n    writeJsonFile(REWRITE_HISTORY_FILE, filteredHistories);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAsEA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,kBAAkB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,uBAAuB,4GAAI,CAAC,IAAI,CAAC,UAAU;AACjD,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AAEtC,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,WAAW,CAAC,SAAiB;QAC3B,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK,WAAW,UAAU,IAAI,KAAK;IAC5F;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,IAAwB,aAA6B;IAE7D,cAAc,CAAC;QACb,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK,SAAS,IAAI,CAAC,CAAC,GAAG,IACvE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IAEnE;IAEA,SAAS,CAAC;QACR,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAClD;IAEA,YAAY,CAAC;QACX,MAAM,YAAY,aAA6B;QAC/C,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,YAAY,aAA6B;QAC/C,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,UAAU,IAAI,CAAC;QACf,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,YAAY,aAA6B;QAC/C,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC5D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,MAAM,CAAC,OAAO;QACxB,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,YAAY,aAA6B;QAC/C,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QAC1E,cAAc,sBAAsB;QACpC,OAAO;IACT;IAEA,eAAe,CAAC;QACd,MAAM,YAAY,aAA6B;QAC/C,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;QACxE,cAAc,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-rewriter/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成\nconst GEMINI_API_KEY = 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc';\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  characters?: Character[];\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n}\n\nexport interface Character {\n  name: string;\n  role: 'male_lead' | 'female_lead' | 'supporting' | 'villain' | 'other';\n  description: string;\n  personality?: string;\n  appearance?: string;\n  relationships?: string;\n}\n\n// 构建人物信息提示词\nfunction buildCharacterPrompt(characters: Character[]): string {\n  if (!characters || characters.length === 0) {\n    return '';\n  }\n\n  const characterInfo = characters.map(char => {\n    let info = `${char.name}（${getRoleDisplayName(char.role)}）：${char.description}`;\n    \n    if (char.personality) {\n      info += `\\n  性格：${char.personality}`;\n    }\n    \n    if (char.appearance) {\n      info += `\\n  外貌：${char.appearance}`;\n    }\n    \n    if (char.relationships) {\n      info += `\\n  关系：${char.relationships}`;\n    }\n    \n    return info;\n  }).join('\\n\\n');\n\n  return `\\n\\n人物信息：\\n${characterInfo}\\n\\n请在改写时参考以上人物信息，确保人物行为和对话符合其设定。`;\n}\n\n// 获取角色显示名称\nfunction getRoleDisplayName(role: Character['role']): string {\n  const roleMap = {\n    'male_lead': '男主角',\n    'female_lead': '女主角',\n    'supporting': '配角',\n    'villain': '反派',\n    'other': '其他'\n  };\n  return roleMap[role] || '其他';\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, characters, chapterTitle, chapterNumber } = request;\n\n  const characterPrompt = buildCharacterPrompt(characters || []);\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}${characterPrompt}\n\n${chapterTitle ? `章节标题：${chapterTitle}` : ''}\n${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n5. 如果提供了人物信息，请确保人物行为和对话符合其设定\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  try {\n    const prompt = buildPrompt(request);\n\n    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.7,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192,\n        },\n        safetySettings: [\n          {\n            category: \"HARM_CATEGORY_HARASSMENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_HATE_SPEECH\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          },\n          {\n            category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n            threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n          }\n        ]\n      }),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error('Gemini API error:', response.status, errorText);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: `API请求失败: ${response.status} ${response.statusText}`,\n      };\n    }\n\n    const data = await response.json();\n\n    if (!data.candidates || data.candidates.length === 0) {\n      console.error('No candidates in response:', data);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: 'API返回的数据格式不正确',\n      };\n    }\n\n    const candidate = data.candidates[0];\n    \n    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n      console.error('No content in candidate:', candidate);\n      return {\n        rewrittenText: '',\n        success: false,\n        error: 'API返回的内容为空',\n      };\n    }\n\n    const rewrittenText = candidate.content.parts[0].text;\n\n    if (!rewrittenText || rewrittenText.trim().length === 0) {\n      return {\n        rewrittenText: '',\n        success: false,\n        error: '改写结果为空',\n      };\n    }\n\n    return {\n      rewrittenText: rewrittenText.trim(),\n      success: true,\n    };\n\n  } catch (error) {\n    console.error('Gemini API call failed:', error);\n    return {\n      rewrittenText: '',\n      success: false,\n      error: `网络请求失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    };\n  }\n}\n\n// 批量改写多个章节（支持并发）\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  characters: Character[] = [],\n  onProgress?: (progress: number, currentChapter: number) => void,\n  concurrency: number = 10\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  const results: Array<{ success: boolean; content: string; error?: string }> = new Array(chapters.length);\n  let completed = 0;\n\n  // 分批处理，每批最多 concurrency 个章节\n  const batches: Array<Array<{ content: string; title: string; number: number; index: number }>> = [];\n\n  for (let i = 0; i < chapters.length; i += concurrency) {\n    const batch = chapters.slice(i, i + concurrency).map((chapter, batchIndex) => ({\n      ...chapter,\n      index: i + batchIndex\n    }));\n    batches.push(batch);\n  }\n\n  for (const batch of batches) {\n    // 并发处理当前批次\n    const batchPromises = batch.map(async (chapter) => {\n      try {\n        const result = await rewriteText({\n          originalText: chapter.content,\n          rules,\n          characters,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n        });\n\n        results[chapter.index] = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n        };\n\n        completed++;\n\n        // 更新进度\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return result;\n      } catch (error) {\n        results[chapter.index] = {\n          success: false,\n          content: '',\n          error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        };\n\n        completed++;\n\n        if (onProgress) {\n          onProgress((completed / chapters.length) * 100, chapter.number);\n        }\n\n        return null;\n      }\n    });\n\n    // 等待当前批次完成\n    await Promise.all(batchPromises);\n\n    // 批次间稍作延迟，避免API限流\n    if (batches.indexOf(batch) < batches.length - 1) {\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n  }\n\n  return results;\n}\n\n// 预设的改写规则模板\nexport const PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  dialogue_enhancement: {\n    name: '对话优化',\n    description: '优化人物对话，使其更加生动自然',\n    rules: `请按照以下规则改写：\n1. 优化人物对话，使其更符合角色性格和身份\n2. 增加对话的层次感和潜台词\n3. 删除过于直白或生硬的对话\n4. 通过对话推进情节发展和人物关系`\n  }\n};\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;;;AAChB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AAyBvB,YAAY;AACZ,SAAS,qBAAqB,UAAuB;IACnD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO;IACT;IAEA,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAA;QACnC,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,mBAAmB,KAAK,IAAI,EAAE,EAAE,EAAE,KAAK,WAAW,EAAE;QAE/E,IAAI,KAAK,WAAW,EAAE;YACpB,QAAQ,CAAC,OAAO,EAAE,KAAK,WAAW,EAAE;QACtC;QAEA,IAAI,KAAK,UAAU,EAAE;YACnB,QAAQ,CAAC,OAAO,EAAE,KAAK,UAAU,EAAE;QACrC;QAEA,IAAI,KAAK,aAAa,EAAE;YACtB,QAAQ,CAAC,OAAO,EAAE,KAAK,aAAa,EAAE;QACxC;QAEA,OAAO;IACT,GAAG,IAAI,CAAC;IAER,OAAO,CAAC,WAAW,EAAE,cAAc,iCAAiC,CAAC;AACvE;AAEA,WAAW;AACX,SAAS,mBAAmB,IAAuB;IACjD,MAAM,UAAU;QACd,aAAa;QACb,eAAe;QACf,cAAc;QACd,WAAW;QACX,SAAS;IACX;IACA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B;AAEA,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAEzE,MAAM,kBAAkB,qBAAqB,cAAc,EAAE;IAE7D,OAAO,CAAC;;;AAGV,EAAE,QAAQ,gBAAgB;;AAE1B,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,GAAG,GAAG;AAC7C,EAAE,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,GAAG,GAAG;;;AAGjD,EAAE,aAAa;;;;;;;;;wBASS,CAAC;AACzB;AAGO,eAAe,YAAY,OAAuB;IACvD,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,gBAAgB,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBACT,OAAO;4BAAC;gCACN,MAAM;4BACR;yBAAE;oBACJ;iBAAE;gBACF,kBAAkB;oBAChB,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,iBAAiB;gBACnB;gBACA,gBAAgB;oBACd;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,UAAU;wBACV,WAAW;oBACb;iBACD;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qBAAqB,SAAS,MAAM,EAAE;YACpD,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC7D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;QAEpC,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAC1F,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QAErD,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO;gBACL,eAAe;gBACf,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,eAAe,cAAc,IAAI;YACjC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,eAAe;YACf,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACrE;IACF;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,aAA0B,EAAE,EAC5B,UAA+D,EAC/D,cAAsB,EAAE;IAExB,MAAM,UAAwE,IAAI,MAAM,SAAS,MAAM;IACvG,IAAI,YAAY;IAEhB,4BAA4B;IAC5B,MAAM,UAA2F,EAAE;IAEnG,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,YAAa;QACrD,MAAM,QAAQ,SAAS,KAAK,CAAC,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC,SAAS,aAAe,CAAC;gBAC7E,GAAG,OAAO;gBACV,OAAO,IAAI;YACb,CAAC;QACD,QAAQ,IAAI,CAAC;IACf;IAEA,KAAK,MAAM,SAAS,QAAS;QAC3B,WAAW;QACX,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;YACrC,IAAI;gBACF,MAAM,SAAS,MAAM,YAAY;oBAC/B,cAAc,QAAQ,OAAO;oBAC7B;oBACA;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;gBAC/B;gBAEA,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;gBACrB;gBAEA;gBAEA,OAAO;gBACP,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAG;oBACvB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACnE;gBAEA;gBAEA,IAAI,YAAY;oBACd,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM;gBAChE;gBAEA,OAAO;YACT;QACF;QAEA,WAAW;QACX,MAAM,QAAQ,GAAG,CAAC;QAElB,kBAAkB;QAClB,IAAI,QAAQ,OAAO,CAAC,SAAS,QAAQ,MAAM,GAAG,GAAG;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;qBAIS,CAAC;IACpB;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;oBAIQ,CAAC;IACnB;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;eAIG,CAAC;IACd;IAEA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;AACF", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-rewriter/src/app/api/rules/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ruleDb } from '@/lib/database';\nimport { PRESET_RULES } from '@/lib/gemini';\n\n// GET - 获取规则列表\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const includePresets = searchParams.get('includePresets') === 'true';\n    \n    // 获取用户自定义规则\n    const customRules = ruleDb.getAll();\n    \n    if (includePresets) {\n      // 包含预设规则\n      const presetRules = Object.entries(PRESET_RULES).map(([key, preset]) => ({\n        id: `preset_${key}`,\n        name: preset.name,\n        description: preset.description,\n        rules: preset.rules,\n        isPreset: true,\n        createdAt: '',\n        updatedAt: '',\n      }));\n      \n      return NextResponse.json({\n        success: true,\n        data: {\n          customRules,\n          presetRules,\n          allRules: [...presetRules, ...customRules],\n        },\n      });\n    } else {\n      // 只返回用户自定义规则\n      return NextResponse.json({\n        success: true,\n        data: customRules,\n      });\n    }\n  } catch (error) {\n    console.error('获取规则列表失败:', error);\n    return NextResponse.json(\n      { success: false, error: '获取规则列表失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - 创建新规则\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { name, description, rules } = body;\n\n    // 验证必填字段\n    if (!name || !description || !rules) {\n      return NextResponse.json(\n        { success: false, error: '规则名称、描述和内容不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 检查规则名称是否已存在\n    const existingRules = ruleDb.getAll();\n    const duplicateName = existingRules.find(rule => rule.name === name);\n    if (duplicateName) {\n      return NextResponse.json(\n        { success: false, error: '规则名称已存在' },\n        { status: 400 }\n      );\n    }\n\n    // 创建规则\n    const rule = ruleDb.create({\n      name: name.trim(),\n      description: description.trim(),\n      rules: rules.trim(),\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: rule,\n      message: '规则创建成功',\n    });\n\n  } catch (error) {\n    console.error('创建规则失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建规则失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT - 更新规则\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { id, name, description, rules } = body;\n\n    if (!id) {\n      return NextResponse.json(\n        { success: false, error: '规则ID不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 检查规则是否存在\n    const existingRule = ruleDb.getById(id);\n    if (!existingRule) {\n      return NextResponse.json(\n        { success: false, error: '规则不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 如果更新了名称，检查是否与其他规则重名\n    if (name && name !== existingRule.name) {\n      const existingRules = ruleDb.getAll();\n      const duplicateName = existingRules.find(rule => rule.id !== id && rule.name === name);\n      if (duplicateName) {\n        return NextResponse.json(\n          { success: false, error: '规则名称已存在' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 更新规则信息\n    const updates: any = {};\n    if (name !== undefined) updates.name = name.trim();\n    if (description !== undefined) updates.description = description.trim();\n    if (rules !== undefined) updates.rules = rules.trim();\n\n    const updatedRule = ruleDb.update(id, updates);\n\n    if (!updatedRule) {\n      return NextResponse.json(\n        { success: false, error: '更新规则失败' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: updatedRule,\n      message: '规则更新成功',\n    });\n\n  } catch (error) {\n    console.error('更新规则失败:', error);\n    return NextResponse.json(\n      { success: false, error: '更新规则失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE - 删除规则\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const id = searchParams.get('id');\n\n    if (!id) {\n      return NextResponse.json(\n        { success: false, error: '规则ID不能为空' },\n        { status: 400 }\n      );\n    }\n\n    // 检查是否为预设规则\n    if (id.startsWith('preset_')) {\n      return NextResponse.json(\n        { success: false, error: '不能删除预设规则' },\n        { status: 400 }\n      );\n    }\n\n    // 检查规则是否存在\n    const rule = ruleDb.getById(id);\n    if (!rule) {\n      return NextResponse.json(\n        { success: false, error: '规则不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 删除规则\n    const success = ruleDb.delete(id);\n\n    if (!success) {\n      return NextResponse.json(\n        { success: false, error: '删除规则失败' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: '规则删除成功',\n    });\n\n  } catch (error) {\n    console.error('删除规则失败:', error);\n    return NextResponse.json(\n      { success: false, error: '删除规则失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,iBAAiB,aAAa,GAAG,CAAC,sBAAsB;QAE9D,YAAY;QACZ,MAAM,cAAc,kIAAM,CAAC,MAAM;QAEjC,IAAI,gBAAgB;YAClB,SAAS;YACT,MAAM,cAAc,OAAO,OAAO,CAAC,sIAAY,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK,CAAC;oBACvE,IAAI,CAAC,OAAO,EAAE,KAAK;oBACnB,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,OAAO,OAAO,KAAK;oBACnB,UAAU;oBACV,WAAW;oBACX,WAAW;gBACb,CAAC;YAED,OAAO,gJAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ;oBACA;oBACA,UAAU;2BAAI;2BAAgB;qBAAY;gBAC5C;YACF;QACF,OAAO;YACL,aAAa;YACb,OAAO,gJAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;QAErC,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO;YACnC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAiB,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,gBAAgB,kIAAM,CAAC,MAAM;QACnC,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAC/D,IAAI,eAAe;YACjB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,OAAO,kIAAM,CAAC,MAAM,CAAC;YACzB,MAAM,KAAK,IAAI;YACf,aAAa,YAAY,IAAI;YAC7B,OAAO,MAAM,IAAI;QACnB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;QAEzC,IAAI,CAAC,IAAI;YACP,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,eAAe,kIAAM,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,cAAc;YACjB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI,QAAQ,SAAS,aAAa,IAAI,EAAE;YACtC,MAAM,gBAAgB,kIAAM,CAAC,MAAM;YACnC,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,KAAK,IAAI,KAAK;YACjF,IAAI,eAAe;gBACjB,OAAO,gJAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAU,GACnC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,SAAS;QACT,MAAM,UAAe,CAAC;QACtB,IAAI,SAAS,WAAW,QAAQ,IAAI,GAAG,KAAK,IAAI;QAChD,IAAI,gBAAgB,WAAW,QAAQ,WAAW,GAAG,YAAY,IAAI;QACrE,IAAI,UAAU,WAAW,QAAQ,KAAK,GAAG,MAAM,IAAI;QAEnD,MAAM,cAAc,kIAAM,CAAC,MAAM,CAAC,IAAI;QAEtC,IAAI,CAAC,aAAa;YAChB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,IAAI,GAAG,UAAU,CAAC,YAAY;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,OAAO,kIAAM,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,UAAU,kIAAM,CAAC,MAAM,CAAC;QAE9B,IAAI,CAAC,SAAS;YACZ,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}