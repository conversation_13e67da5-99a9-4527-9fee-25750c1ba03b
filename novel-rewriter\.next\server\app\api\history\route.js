var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/history/route.js")
R.c("server/chunks/node_modules_next_ad9eb344._.js")
R.c("server/chunks/[root-of-the-server]__99393b06._.js")
R.m("[project]/.next-internal/server/app/api/history/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/history/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/history/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
