import { NextResponse } from 'next/server';
import { getApiKeyStats } from '@/lib/gemini';

// GET - 获取API Key使用统计
export async function GET() {
  try {
    const stats = getApiKeyStats();
    
    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('获取API统计失败:', error);
    return NextResponse.json(
      { success: false, error: '获取API统计失败' },
      { status: 500 }
    );
  }
}
