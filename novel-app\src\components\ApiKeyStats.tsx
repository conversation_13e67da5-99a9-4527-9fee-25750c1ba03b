'use client';

import { useState, useEffect } from 'react';
import { RefreshCw, Key, Activity, Clock, CheckCircle, XCircle } from 'lucide-react';

interface ApiKeyStatsProps {
  refreshInterval?: number; // 刷新间隔（毫秒）
}

interface ApiKeyStats {
  name: string;
  requestCount: number;
  weight: number;
  isAvailable: boolean;
  cooldownRemaining?: number;
}

export default function ApiKeyStats({ refreshInterval = 5000 }: ApiKeyStatsProps) {
  const [stats, setStats] = useState<ApiKeyStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    loadStats();
    
    if (refreshInterval > 0) {
      const interval = setInterval(loadStats, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);

  const loadStats = async () => {
    try {
      const response = await fetch('/api/gemini/stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('加载API统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/gemini/test');
      const result = await response.json();
      
      if (result.success) {
        alert(`连接测试成功！\n使用的API Key: ${result.details?.apiKeyUsed}\nToken消耗: ${result.details?.tokensUsed}\n处理时间: ${result.details?.processingTime}ms`);
      } else {
        alert(`连接测试失败: ${result.error}`);
      }
      
      // 测试后刷新统计
      await loadStats();
    } catch (error) {
      alert(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const resetStats = async () => {
    if (!confirm('确定要重置所有API Key统计吗？')) return;
    
    try {
      const response = await fetch('/api/gemini/reset', {
        method: 'POST',
      });
      
      if (response.ok) {
        await loadStats();
        alert('统计已重置');
      }
    } catch (error) {
      alert(`重置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const formatCooldown = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.ceil(seconds / 60);
    return `${minutes}分钟`;
  };

  if (loading && stats.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">加载API统计中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Key className="mr-2 text-blue-600" size={20} />
            <h3 className="text-lg font-semibold text-gray-800">API Key 状态</h3>
          </div>
          <div className="flex items-center space-x-2">
            {lastUpdated && (
              <span className="text-xs text-gray-500">
                更新于 {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <button
              onClick={loadStats}
              disabled={loading}
              className="flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
            >
              <RefreshCw size={14} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {stats.length === 0 ? (
          <div className="text-center text-gray-500 py-4">
            暂无API Key统计数据
          </div>
        ) : (
          <div className="space-y-4">
            {/* 总体统计 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <Activity className="text-blue-600 mr-2" size={16} />
                  <div>
                    <div className="text-sm text-blue-600">总请求数</div>
                    <div className="text-lg font-semibold text-blue-800">
                      {stats.reduce((sum, stat) => sum + stat.requestCount, 0)}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="text-green-600 mr-2" size={16} />
                  <div>
                    <div className="text-sm text-green-600">可用Key</div>
                    <div className="text-lg font-semibold text-green-800">
                      {stats.filter(stat => stat.isAvailable).length}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-red-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="text-red-600 mr-2" size={16} />
                  <div>
                    <div className="text-sm text-red-600">冷却中</div>
                    <div className="text-lg font-semibold text-red-800">
                      {stats.filter(stat => !stat.isAvailable).length}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-purple-50 p-3 rounded-lg">
                <div className="flex items-center">
                  <Key className="text-purple-600 mr-2" size={16} />
                  <div>
                    <div className="text-sm text-purple-600">总权重</div>
                    <div className="text-lg font-semibold text-purple-800">
                      {stats.reduce((sum, stat) => sum + stat.weight, 0)}x
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 详细Key状态 */}
            <div className="space-y-3">
              {stats.map((stat, index) => (
                <div key={index} className={`p-4 rounded-lg border ${
                  stat.isAvailable 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        stat.isAvailable ? 'bg-green-500' : 'bg-red-500'
                      }`}></div>
                      <div>
                        <div className="font-medium text-gray-800">{stat.name}</div>
                        <div className="text-sm text-gray-600">
                          权重: {stat.weight}x | 使用次数: {stat.requestCount}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        stat.isAvailable ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.isAvailable ? '可用' : '冷却中'}
                      </div>
                      {!stat.isAvailable && stat.cooldownRemaining && stat.cooldownRemaining > 0 && (
                        <div className="text-xs text-red-500 flex items-center">
                          <Clock size={12} className="mr-1" />
                          {formatCooldown(stat.cooldownRemaining)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4 mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={testConnection}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? '测试中...' : '测试连接'}
          </button>
          
          <button
            onClick={resetStats}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            重置统计
          </button>
        </div>
      </div>
    </div>
  );
}
