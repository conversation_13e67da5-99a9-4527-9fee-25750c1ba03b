'use client';

import { useState } from 'react';

import RewriteProgress from './RewriteProgress';
import <PERSON>Hist<PERSON> from './JobHistory';
import ApiKeyStats from './ApiKeyStats';
import { Activity, History, Key, Eye } from 'lucide-react';

interface TaskManagerProps {
  currentJobId?: string;
  onJobComplete?: () => void;
}

export default function TaskManager({ currentJobId, onJobComplete }: TaskManagerProps) {
  const [selectedJobId, setSelectedJobId] = useState<string | null>(currentJobId || null);
  const [activeTab, setActiveTab] = useState(currentJobId ? 'current' : 'history');

  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId);
    setActiveTab('current');
  };

  const handleJobComplete = () => {
    if (onJobComplete) {
      onJobComplete();
    }
    // 任务完成后切换到历史页面
    setTimeout(() => {
      setActiveTab('history');
    }, 2000);
  };

  const tabs = [
    { id: 'current', label: '当前任务', icon: Activity },
    { id: 'history', label: '任务历史', icon: History },
    { id: 'stats', label: 'API状态', icon: Key },
  ];

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors flex-1 justify-center ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Icon className="mr-2" size={16} />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'current' && (
          <div>
            {selectedJobId ? (
              <RewriteProgress
                jobId={selectedJobId}
                onComplete={handleJobComplete}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <Eye className="mx-auto mb-4 text-gray-400" size={48} />
                <h3 className="text-lg font-medium text-gray-800 mb-2">没有正在进行的任务</h3>
                <p className="text-gray-600 mb-4">
                  当前没有正在执行的改写任务。你可以：
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• 从任务历史中选择一个任务查看详情</p>
                  <p>• 创建新的改写任务</p>
                  <p>• 查看API Key使用状态</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <JobHistory onJobSelect={handleJobSelect} />
        )}

        {activeTab === 'stats' && (
          <ApiKeyStats refreshInterval={5000} />
        )}
      </div>
    </div>
  );
}
