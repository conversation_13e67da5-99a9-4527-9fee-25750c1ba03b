import { NextRequest, NextResponse } from 'next/server';
import { jobDb } from '@/lib/database';

// GET - 获取任务列表或单个任务状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (jobId) {
      // 获取单个任务状态
      const job = jobDb.getById(jobId);
      if (!job) {
        return NextResponse.json(
          { success: false, error: '任务不存在' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: job,
      });
    } else {
      // 获取所有任务
      const jobs = jobDb.getAll();
      return NextResponse.json({
        success: true,
        data: jobs,
      });
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    return NextResponse.json(
      { success: false, error: '获取任务信息失败' },
      { status: 500 }
    );
  }
}

// DELETE - 删除任务记录
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: '缺少任务ID' },
        { status: 400 }
      );
    }

    const success = jobDb.delete(jobId);

    if (!success) {
      return NextResponse.json(
        { success: false, error: '任务不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '任务记录已删除',
    });
  } catch (error) {
    console.error('删除任务记录失败:', error);
    return NextResponse.json(
      { success: false, error: '删除任务记录失败' },
      { status: 500 }
    );
  }
}
