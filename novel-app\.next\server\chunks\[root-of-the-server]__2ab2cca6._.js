module.exports = [
"[project]/.next-internal/server/app/api/gemini/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/lib/gemini.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// Gemini API 集成 - 多Key池管理
// API Keys配置 - 第一个key是其他4个的4倍强度
__turbopack_context__.s([
    "PRESET_RULES",
    ()=>PRESET_RULES,
    "addCustomPreset",
    ()=>addCustomPreset,
    "getApiKeyStats",
    ()=>getApiKeyStats,
    "resetApiKeyStats",
    ()=>resetApiKeyStats,
    "rewriteChapters",
    ()=>rewriteChapters,
    "rewriteText",
    ()=>rewriteText,
    "testGeminiConnection",
    ()=>testGeminiConnection
]);
const API_KEYS = [
    {
        key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw',
        name: 'My First Project',
        weight: 4,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y',
        name: 'ankibot',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY',
        name: 'Generative Language Client',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc',
        name: 'In The Novel',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    },
    {
        key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk',
        name: 'chat',
        weight: 1,
        requestCount: 0,
        lastUsed: 0,
        cooldownUntil: 0
    }
];
// API配置
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';
const REQUEST_DELAY = 1000; // 请求间隔（毫秒）
const COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）
const MAX_RETRIES = 3; // 最大重试次数
// API Key管理类
class ApiKeyManager {
    keys = [
        ...API_KEYS
    ];
    // 获取最佳可用的API Key
    getBestAvailableKey() {
        const now = Date.now();
        // 过滤掉冷却中的key
        const availableKeys = this.keys.filter((key)=>key.cooldownUntil <= now);
        if (availableKeys.length === 0) {
            // 如果所有key都在冷却中，返回冷却时间最短的
            return this.keys.reduce((min, key)=>key.cooldownUntil < min.cooldownUntil ? key : min);
        }
        // 根据权重和使用频率选择最佳key
        const bestKey = availableKeys.reduce((best, key)=>{
            const keyScore = key.weight / (key.requestCount + 1);
            const bestScore = best.weight / (best.requestCount + 1);
            return keyScore > bestScore ? key : best;
        });
        return bestKey;
    }
    // 记录API使用
    recordUsage(keyName, success) {
        const key = this.keys.find((k)=>k.name === keyName);
        if (key) {
            key.requestCount++;
            key.lastUsed = Date.now();
            if (!success) {
                // 如果失败，设置冷却时间
                key.cooldownUntil = Date.now() + COOLDOWN_DURATION;
            }
        }
    }
    // 获取统计信息
    getStats() {
        return this.keys.map((key)=>({
                name: key.name,
                requestCount: key.requestCount,
                weight: key.weight,
                isAvailable: key.cooldownUntil <= Date.now(),
                cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now())
            }));
    }
}
const keyManager = new ApiKeyManager();
// 构建改写提示词
function buildPrompt(request) {
    const { originalText, rules, chapterTitle, chapterNumber } = request;
    return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：

改写规则：
${rules}

${chapterTitle ? `${chapterTitle}` : ''}
// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}

原文内容：
${originalText}

请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：
1. 遵循所有指定的改写规则
2. 保持原文的基本故事框架（除非规则要求修改）
3. 确保文字流畅自然
4. 保持角色的基本性格特征（除非规则要求修改）

请直接输出改写后的内容，不要添加任何解释或说明：`;
}
async function rewriteText(request) {
    const startTime = Date.now();
    let lastError = '';
    for(let attempt = 0; attempt < MAX_RETRIES; attempt++){
        try {
            const apiKey = keyManager.getBestAvailableKey();
            // 如果key在冷却中，等待一段时间
            if (apiKey.cooldownUntil > Date.now()) {
                const waitTime = Math.min(apiKey.cooldownUntil - Date.now(), 5000); // 最多等待5秒
                await new Promise((resolve)=>setTimeout(resolve, waitTime));
            }
            const prompt = buildPrompt(request);
            const response = await fetch(`${GEMINI_API_URL}?key=${apiKey.key}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [
                        {
                            parts: [
                                {
                                    text: prompt
                                }
                            ]
                        }
                    ],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8192
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        }
                    ]
                })
            });
            const processingTime = Date.now() - startTime;
            if (response.status === 429) {
                // 429错误，记录失败并尝试下一个key
                keyManager.recordUsage(apiKey.name, false);
                lastError = `API限流 (${apiKey.name})`;
                if (attempt < MAX_RETRIES - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));
                    continue;
                }
            }
            if (!response.ok) {
                const errorData = await response.text();
                console.error('Gemini API error:', errorData);
                keyManager.recordUsage(apiKey.name, false);
                lastError = `API请求失败: ${response.status} ${response.statusText}`;
                if (attempt < MAX_RETRIES - 1) {
                    await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY));
                    continue;
                }
                return {
                    rewrittenText: '',
                    success: false,
                    error: lastError,
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const data = await response.json();
            // 记录成功使用
            keyManager.recordUsage(apiKey.name, true);
            if (!data.candidates || data.candidates.length === 0) {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '没有收到有效的响应内容',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const candidate = data.candidates[0];
            if (candidate.finishReason === 'SAFETY') {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '内容被安全过滤器拦截，请调整改写规则或原文内容',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                return {
                    rewrittenText: '',
                    success: false,
                    error: '响应内容格式错误',
                    apiKeyUsed: apiKey.name,
                    processingTime
                };
            }
            const rewrittenText = candidate.content.parts[0].text;
            // 尝试从响应中提取token使用信息
            const tokensUsed = data.usageMetadata?.totalTokenCount || 0;
            return {
                rewrittenText: rewrittenText.trim(),
                success: true,
                apiKeyUsed: apiKey.name,
                tokensUsed,
                model: 'gemini-2.5-flash-lite',
                processingTime
            };
        } catch (error) {
            console.error('Gemini API调用错误:', error);
            lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;
            if (attempt < MAX_RETRIES - 1) {
                await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));
            }
        }
    }
    return {
        rewrittenText: '',
        success: false,
        error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,
        processingTime: Date.now() - startTime
    };
}
async function rewriteChapters(chapters, rules, onProgress, onChapterComplete, concurrency = 3 // 降低并发数以避免429错误
) {
    const results = new Array(chapters.length);
    let completed = 0;
    let totalTokensUsed = 0;
    const startTime = Date.now();
    // 使用更保守的并发策略
    const semaphore = new Semaphore(concurrency);
    const processChapter = async (chapter, index)=>{
        await semaphore.acquire();
        const chapterStartTime = Date.now();
        try {
            const result = await rewriteText({
                originalText: chapter.content,
                rules,
                chapterTitle: chapter.title,
                chapterNumber: chapter.number
            });
            const chapterProcessingTime = Date.now() - chapterStartTime;
            if (result.tokensUsed) {
                totalTokensUsed += result.tokensUsed;
            }
            const chapterResult = {
                success: result.success,
                content: result.rewrittenText,
                error: result.error,
                details: {
                    apiKeyUsed: result.apiKeyUsed,
                    tokensUsed: result.tokensUsed,
                    model: result.model,
                    processingTime: chapterProcessingTime,
                    chapterNumber: chapter.number,
                    chapterTitle: chapter.title
                }
            };
            results[index] = chapterResult;
            completed++;
            // 实时回调章节完成
            if (onChapterComplete) {
                onChapterComplete(index, chapterResult);
            }
            // 更新进度，包含详细信息
            if (onProgress) {
                const progressDetails = {
                    completed,
                    total: chapters.length,
                    totalTokensUsed,
                    totalTime: Date.now() - startTime,
                    averageTimePerChapter: (Date.now() - startTime) / completed,
                    apiKeyStats: keyManager.getStats(),
                    currentChapter: {
                        number: chapter.number,
                        title: chapter.title,
                        processingTime: chapterProcessingTime,
                        apiKey: result.apiKeyUsed,
                        tokens: result.tokensUsed
                    }
                };
                onProgress(completed / chapters.length * 100, chapter.number, progressDetails);
            }
            // 添加请求间隔
            await new Promise((resolve)=>setTimeout(resolve, REQUEST_DELAY));
            return result;
        } catch (error) {
            const chapterErrorTime = Date.now();
            const errorResult = {
                success: false,
                content: '',
                error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
                details: {
                    chapterNumber: chapter.number,
                    chapterTitle: chapter.title,
                    processingTime: chapterErrorTime - chapterStartTime
                }
            };
            results[index] = errorResult;
            completed++;
            if (onChapterComplete) {
                onChapterComplete(index, errorResult);
            }
            if (onProgress) {
                const progressDetails = {
                    completed,
                    total: chapters.length,
                    totalTokensUsed,
                    totalTime: Date.now() - startTime,
                    averageTimePerChapter: (Date.now() - startTime) / completed,
                    apiKeyStats: keyManager.getStats(),
                    currentChapter: {
                        number: chapter.number,
                        title: chapter.title,
                        error: error instanceof Error ? error.message : '未知错误'
                    }
                };
                onProgress(completed / chapters.length * 100, chapter.number, progressDetails);
            }
            return null;
        } finally{
            semaphore.release();
        }
    };
    // 并发处理所有章节
    const promises = chapters.map((chapter, index)=>processChapter(chapter, index));
    await Promise.all(promises);
    return results;
}
// 信号量类，用于控制并发
class Semaphore {
    permits;
    waitQueue = [];
    constructor(permits){
        this.permits = permits;
    }
    async acquire() {
        if (this.permits > 0) {
            this.permits--;
            return Promise.resolve();
        }
        return new Promise((resolve)=>{
            this.waitQueue.push(resolve);
        });
    }
    release() {
        this.permits++;
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift();
            if (resolve) {
                this.permits--;
                resolve();
            }
        }
    }
}
async function testGeminiConnection() {
    try {
        const testResult = await rewriteText({
            originalText: '这是一个测试文本。',
            rules: '保持原文不变'
        });
        return {
            success: testResult.success,
            error: testResult.error,
            details: {
                apiKeyUsed: testResult.apiKeyUsed,
                tokensUsed: testResult.tokensUsed,
                model: testResult.model,
                processingTime: testResult.processingTime,
                apiKeyStats: keyManager.getStats()
            }
        };
    } catch (error) {
        return {
            success: false,
            error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
            details: {
                apiKeyStats: keyManager.getStats()
            }
        };
    }
}
function getApiKeyStats() {
    return keyManager.getStats();
}
function resetApiKeyStats() {
    API_KEYS.forEach((key)=>{
        key.requestCount = 0;
        key.lastUsed = 0;
        key.cooldownUntil = 0;
    });
}
let PRESET_RULES = {
    romance_focus: {
        name: '感情戏增强',
        description: '扩写男女主互动内容，对非感情戏部分一笔带过',
        rules: `请按照以下规则改写：
1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节
2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过
3. 增加角色间的情感张力和暧昧氛围
4. 保持故事主线不变，但重点突出感情发展`
    },
    character_fix: {
        name: '人设修正',
        description: '修正主角人设和对话风格',
        rules: `请按照以下规则改写：
1. 修正主角的性格设定，使其更加立体和讨喜
2. 改善对话风格，使其更加自然流畅
3. 去除过于中二或不合理的行为描写
4. 保持角色的核心特征，但优化表现方式`
    },
    toxic_content_removal: {
        name: '毒点清除',
        description: '移除送女、绿帽等毒点情节',
        rules: `请按照以下规则改写：
1. 完全移除或修改送女、绿帽、圣母等毒点情节
2. 删除或改写让读者不适的桥段
3. 保持故事逻辑的完整性
4. 用更合理的情节替代被删除的内容`
    },
    pacing_improvement: {
        name: '节奏优化',
        description: '优化故事节奏，删除拖沓内容',
        rules: `请按照以下规则改写：
1. 删除重复和拖沓的描写
2. 加快故事节奏，突出重点情节
3. 简化过于冗长的对话和心理描写
4. 保持故事的紧凑性和可读性`
    },
    custom: {
        name: '自定义规则',
        description: '用户自定义的改写规则',
        rules: ''
    }
};
function addCustomPreset(name, description, rules) {
    const key = `custom_${Date.now()}`;
    PRESET_RULES = {
        ...PRESET_RULES,
        [key]: {
            name,
            description,
            rules
        }
    };
    return key;
}
}),
"[project]/src/app/api/gemini/stats/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gemini.ts [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        const stats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiKeyStats"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取API统计失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取API统计失败'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__2ab2cca6._.js.map