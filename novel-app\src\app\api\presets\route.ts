import { NextRequest, NextResponse } from 'next/server';
import { addCustomPreset } from '@/lib/gemini';

// POST - 保存自定义预设
export async function POST(request: NextRequest) {
  try {
    const { name, description, rules } = await request.json();

    if (!name || !rules) {
      return NextResponse.json(
        { success: false, error: '名称和规则不能为空' },
        { status: 400 }
      );
    }

    // 添加自定义预设
    const presetKey = addCustomPreset(name, description || '', rules);

    return NextResponse.json({
      success: true,
      data: {
        presetKey,
        message: '预设保存成功',
      },
    });

  } catch (error) {
    console.error('保存预设失败:', error);
    return NextResponse.json(
      { success: false, error: '保存预设失败' },
      { status: 500 }
    );
  }
}
