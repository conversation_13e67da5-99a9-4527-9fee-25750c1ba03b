'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import NovelSelector from '@/components/NovelSelector';
import ChapterSelector from '@/components/ChapterSelector';
import RuleEditor from '@/components/RuleEditor';
import CharacterManager from '@/components/CharacterManager';
import RewriteProgress from '@/components/RewriteProgress';
import TaskManager from '@/components/TaskManager';
import Toast from '@/components/Toast';
import { Novel, Chapter } from '@/lib/database';
import { HelpCircle } from 'lucide-react';

interface Character {
  id: string;
  name: string;
  type: '男主' | '女主' | '配角' | '反派' | '其他';
  description: string;
}

interface ToastState {
  show: boolean;
  message: string;
  type: 'success' | 'error' | 'info';
}

export default function Home() {
  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);
  const [selectedChapters, setSelectedChapters] = useState<string>('');
  const [rewriteRules, setRewriteRules] = useState<string>('');
  const [characters, setCharacters] = useState<Character[]>([]);
  const [isRewriting, setIsRewriting] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [showTaskManager, setShowTaskManager] = useState(false);
  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });

  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setToast({ show: true, message, type });
  };

  const hideToast = () => {
    setToast({ show: false, message: '', type: 'info' });
  };

  const handleSaveToPreset = async (rules: string) => {
    const name = prompt('请输入预设名称:');
    if (!name) return;

    const description = prompt('请输入预设描述 (可选):') || '';

    try {
      const response = await fetch('/api/presets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          rules,
        }),
      });

      const result = await response.json();

      if (result.success) {
        showToast('规则已保存到预设', 'success');
      } else {
        showToast(`保存失败: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('保存预设失败:', error);
      showToast('保存预设失败', 'error');
    }
  };

  const handleStartRewrite = async () => {
    if (!selectedNovel || !selectedChapters || !rewriteRules) {
      showToast('请完整填写所有信息', 'error');
      return;
    }

    setIsRewriting(true);

    try {
      // 构建包含人物信息的改写规则
      let enhancedRules = rewriteRules;
      if (characters.length > 0) {
        const characterInfo = characters.map(char =>
          `${char.name}(${char.type}${char.description ? ': ' + char.description : ''})`
        ).join('、');
        enhancedRules = `人物设定：${characterInfo}\n\n${rewriteRules}`;
      }

      const response = await fetch('/api/rewrite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          novelId: selectedNovel.id,
          chapterRange: selectedChapters,
          rules: enhancedRules,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentJobId(result.data.jobId);
        showToast('改写任务已开始', 'info');
      } else {
        showToast(`改写失败: ${result.error}`, 'error');
        setIsRewriting(false);
      }
    } catch (error) {
      console.error('改写请求失败:', error);
      showToast('改写请求失败', 'error');
      setIsRewriting(false);
    }
  };

  const handleRewriteComplete = () => {
    setIsRewriting(false);
    setCurrentJobId(null);
    showToast('改写完成！', 'success');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-4">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-800">
            小说改写工具
          </h1>
          <div className="flex items-center space-x-4">
            {/* 任务管理按钮 */}
            <button
              onClick={() => setShowTaskManager(!showTaskManager)}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="mr-1" width={18} height={18} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 9h6v6H9z"/>
              </svg>
              任务管理
            </button>
            {/* 开始改写按钮 */}
            <button
              onClick={handleStartRewrite}
              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors"
            >
              {isRewriting ? '改写中...' : '开始改写'}
            </button>
            <Link
              href="/help"
              className="flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <HelpCircle className="mr-1" size={18} />
              帮助
            </Link>
          </div>
        </div>

        {/* 任务管理器 */}
        {showTaskManager && (
          <div className="mb-6">
            <TaskManager
              currentJobId={currentJobId}
              onJobComplete={handleRewriteComplete}
            />
          </div>
        )}

        {/* 进度显示 */}
        {isRewriting && currentJobId && !showTaskManager && (
          <div className="mb-4">
            <RewriteProgress
              jobId={currentJobId}
              onComplete={handleRewriteComplete}
            />
          </div>
        )}

        {!showTaskManager && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* 左侧：改写规则 */}
          <div className="lg:col-span-1">
            <RuleEditor
              rules={rewriteRules}
              onRulesChange={setRewriteRules}
              onSaveToPreset={handleSaveToPreset}
              disabled={isRewriting}
            />
          </div>

          {/* 中间：小说选择和人物管理 */}
          <div className="lg:col-span-1 space-y-4">
            <NovelSelector
              selectedNovel={selectedNovel}
              onNovelSelect={setSelectedNovel}
              disabled={isRewriting}
            />
            <CharacterManager
              characters={characters}
              onCharactersChange={setCharacters}
              disabled={isRewriting}
            />
          </div>

          {/* 右侧：章节选择 */}
          <div className="lg:col-span-1">
            <ChapterSelector
              novel={selectedNovel}
              selectedChapters={selectedChapters}
              onChaptersChange={setSelectedChapters}
              disabled={isRewriting}
            />
          </div>
        </div>
        )}
      </div>

      {/* Toast 通知 */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
    </div>
  );
}
