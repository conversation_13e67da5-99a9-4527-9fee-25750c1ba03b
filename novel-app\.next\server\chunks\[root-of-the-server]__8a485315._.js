module.exports = [
"[project]/.next-internal/server/app/api/novels/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterDb",
    ()=>chapterDb,
    "jobDb",
    ()=>jobDb,
    "novelDb",
    ()=>novelDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapters.json');
const RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
// 确保数据目录存在
function ensureDataDir() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        return [];
    }
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const newNovel = {
            ...novel,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
}),
"[project]/src/lib/file-manager.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FileManager",
    ()=>FileManager,
    "fileManager",
    ()=>fileManager
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileManager {
    static instance;
    baseDir;
    constructor(){
        this.baseDir = process.cwd();
    }
    static getInstance() {
        if (!FileManager.instance) {
            FileManager.instance = new FileManager();
        }
        return FileManager.instance;
    }
    // 确保目录存在
    ensureDir(dirPath) {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dirPath, {
                recursive: true
            });
        }
    }
    // 获取novels目录路径
    getNovelsDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'novels');
    }
    // 获取chapters目录路径
    getChaptersDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'chapters');
    }
    // 获取数据目录路径
    getDataDir() {
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, 'data');
        this.ensureDir(dataDir);
        return dataDir;
    }
    // 获取改写结果目录路径
    getRewrittenDir() {
        const rewrittenDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getDataDir(), 'rewritten');
        this.ensureDir(rewrittenDir);
        return rewrittenDir;
    }
    // 获取特定小说的改写结果目录
    getNovelRewrittenDir(novelTitle) {
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 获取特定小说的章节目录
    getNovelChaptersDir(novelTitle) {
        const chaptersDir = this.getChaptersDir();
        this.ensureDir(chaptersDir);
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(chaptersDir, this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 清理文件名中的非法字符
    sanitizeFilename(filename) {
        return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
    // 读取文件内容
    readFile(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        } catch (error) {
            console.error(`读取文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 写入文件内容
    writeFile(filePath, content) {
        try {
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            this.ensureDir(dir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, content, 'utf-8');
        } catch (error) {
            console.error(`写入文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 检查文件是否存在
    fileExists(filePath) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath);
    }
    // 获取目录中的所有文件
    listFiles(dirPath, extensions) {
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return [];
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            if (extensions) {
                return files.filter((file)=>{
                    const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file).toLowerCase();
                    return extensions.includes(ext);
                });
            }
            return files;
        } catch (error) {
            console.error(`读取目录失败: ${dirPath}`, error);
            return [];
        }
    }
    // 获取文件信息
    getFileStats(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        } catch (error) {
            console.error(`获取文件信息失败: ${filePath}`, error);
            return null;
        }
    }
    // 删除文件
    deleteFile(filePath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除文件失败: ${filePath}`, error);
            return false;
        }
    }
    // 删除目录
    deleteDir(dirPath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].rmSync(dirPath, {
                    recursive: true,
                    force: true
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除目录失败: ${dirPath}`, error);
            return false;
        }
    }
    // 复制文件
    copyFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].copyFileSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 移动文件
    moveFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].renameSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 获取目录大小
    getDirSize(dirPath) {
        let totalSize = 0;
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return 0;
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            for (const file of files){
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file);
                const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
                if (stats.isDirectory()) {
                    totalSize += this.getDirSize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            console.error(`计算目录大小失败: ${dirPath}`, error);
        }
        return totalSize;
    }
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = [
            'B',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    // 创建备份
    createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath);
            const baseName = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath, ext);
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dir, `${baseName}_backup_${timestamp}${ext}`);
            if (this.copyFile(filePath, backupPath)) {
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error(`创建备份失败: ${filePath}`, error);
            return null;
        }
    }
    // 清理旧备份文件
    cleanupBackups(dirPath, maxBackups = 5) {
        try {
            const files = this.listFiles(dirPath);
            const backupFiles = files.filter((file)=>file.includes('_backup_')).map((file)=>({
                    name: file,
                    path: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file),
                    stats: this.getFileStats(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file))
                })).filter((item)=>item.stats !== null).sort((a, b)=>b.stats.mtime.getTime() - a.stats.mtime.getTime());
            // 删除超出数量限制的备份文件
            if (backupFiles.length > maxBackups) {
                const filesToDelete = backupFiles.slice(maxBackups);
                for (const file of filesToDelete){
                    this.deleteFile(file.path);
                }
            }
        } catch (error) {
            console.error(`清理备份文件失败: ${dirPath}`, error);
        }
    }
}
const fileManager = FileManager.getInstance();
}),
"[project]/src/lib/novel-parser.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getAvailableNovels",
    ()=>getAvailableNovels,
    "isNovelParsed",
    ()=>isNovelParsed,
    "parseChapterRange",
    ()=>parseChapterRange,
    "parseNovelFile",
    ()=>parseNovelFile,
    "reparseNovel",
    ()=>reparseNovel
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
;
;
;
;
// 默认章节匹配模式
const DEFAULT_CHAPTER_PATTERNS = [
    // 一个正则表达式处理多种情况
    /^\s*(?:第[一二三四五六七八九十百千万\d]+[章节卷回集]|Chapter\s+\d+)\s*.*$/gmi
];
async function parseNovelFile(filePath) {
    const filename = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath);
    const title = filename.replace(/\.(txt|md)$/i, '');
    // 读取文件内容
    const content = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
    // 创建小说记录
    const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].create({
        title,
        filename
    });
    // 解析章节
    const chapters = parseChapters(content, novel.id);
    // 批量创建章节记录
    const createdChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].createBatch(chapters);
    // 更新小说的章节数量
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].update(novel.id, {
        chapterCount: createdChapters.length
    });
    // 保存章节文件
    await saveChapterFiles(createdChapters);
    return {
        novel: {
            ...novel,
            chapterCount: createdChapters.length
        },
        chapters: createdChapters
    };
}
// 解析章节内容
function parseChapters(content, novelId) {
    const chapters = [];
    // 尝试不同的章节匹配模式
    let bestPattern = null;
    let bestMatches = [];
    for (const pattern of DEFAULT_CHAPTER_PATTERNS){
        const matches = Array.from(content.matchAll(pattern));
        console.log(`Pattern ${pattern.source} found ${matches.length} matches`);
        if (matches.length > bestMatches.length) {
            bestPattern = pattern;
            bestMatches = matches;
        }
    }
    console.log(`Best pattern found ${bestMatches.length} chapters`);
    if (!bestPattern || bestMatches.length === 0) {
        // 如果没有找到章节标记，将整个文件作为一章
        chapters.push({
            novelId,
            chapterNumber: 1,
            title: '全文',
            content: content.trim(),
            filename: `chapter_1.txt`
        });
        return chapters;
    }
    // 根据匹配结果分割章节
    const chapterPositions = bestMatches.map((match, index)=>({
            index: match.index,
            title: extractChapterTitle(match[0]),
            chapterNumber: index + 1
        }));
    for(let i = 0; i < chapterPositions.length; i++){
        const currentPos = chapterPositions[i];
        const nextPos = chapterPositions[i + 1];
        const startIndex = currentPos.index;
        const endIndex = nextPos ? nextPos.index : content.length;
        const chapterContent = content.slice(startIndex, endIndex).trim();
        if (chapterContent.length > 50) {
            chapters.push({
                novelId,
                chapterNumber: currentPos.chapterNumber,
                title: currentPos.title || `第${currentPos.chapterNumber}章`,
                content: chapterContent,
                filename: `chapter_${currentPos.chapterNumber}.txt`
            });
        }
    }
    console.log(`Successfully parsed ${chapters.length} chapters`);
    return chapters;
}
// 提取章节标题
function extractChapterTitle(chapterText) {
    const lines = chapterText.split('\n');
    const firstLine = lines[0].trim();
    // 如果第一行看起来像标题，使用它
    if (firstLine.length < 100 && firstLine.length > 0) {
        return firstLine;
    }
    // 否则尝试从前几行中找到标题
    for(let i = 0; i < Math.min(3, lines.length); i++){
        const line = lines[i].trim();
        if (line.length > 0 && line.length < 100) {
            return line;
        }
    }
    return '未命名章节';
}
// 保存章节文件到chapters目录
async function saveChapterFiles(chapters) {
    // 为每个小说创建子目录
    const novelIds = [
        ...new Set(chapters.map((ch)=>ch.novelId))
    ];
    for (const novelId of novelIds){
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(novelId);
        if (!novel) continue;
        const novelDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelChaptersDir(novel.title);
        // 保存该小说的所有章节
        const novelChapters = chapters.filter((ch)=>ch.novelId === novelId);
        for (const chapter of novelChapters){
            const chapterPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelDir, chapter.filename);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(chapterPath, chapter.content);
        }
    }
}
function getAvailableNovels() {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].listFiles(novelsDir, [
        '.txt',
        '.md'
    ]);
}
function isNovelParsed(filename) {
    const novels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    return novels.some((novel)=>novel.filename === filename);
}
async function reparseNovel(filename) {
    const novelsDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelsDir();
    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelsDir, filename);
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].fileExists(filePath)) {
        return null;
    }
    // 删除旧的小说和章节数据
    const existingNovels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
    const existingNovel = existingNovels.find((novel)=>novel.filename === filename);
    if (existingNovel) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].deleteByNovelId(existingNovel.id);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].delete(existingNovel.id);
    }
    // 重新解析
    return await parseNovelFile(filePath);
}
function parseChapterRange(rangeStr, maxChapter) {
    const chapters = [];
    const parts = rangeStr.split(',').map((part)=>part.trim());
    for (const part of parts){
        if (part.includes('-')) {
            // 范围格式 (例如: "1-5")
            const [start, end] = part.split('-').map((num)=>parseInt(num.trim()));
            if (!isNaN(start) && !isNaN(end) && start <= end) {
                for(let i = start; i <= Math.min(end, maxChapter); i++){
                    if (i > 0 && !chapters.includes(i)) {
                        chapters.push(i);
                    }
                }
            }
        } else {
            // 单个章节
            const chapterNum = parseInt(part);
            if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
                chapters.push(chapterNum);
            }
        }
    }
    return chapters.sort((a, b)=>a - b);
}
}),
"[project]/src/app/api/novels/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/novel-parser.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
async function GET() {
    try {
        const novels = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getAll();
        const availableFiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableNovels"])();
        // 标记哪些文件已经被解析
        const novelsWithStatus = availableFiles.map((filename)=>{
            const parsed = novels.find((novel)=>novel.filename === filename);
            return {
                filename,
                parsed: !!parsed,
                novel: parsed || null
            };
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                novels: novels,
                availableFiles: novelsWithStatus
            }
        });
    } catch (error) {
        console.error('获取小说列表失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取小说列表失败'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { filename, reparse = false } = await request.json();
        if (!filename) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '文件名不能为空'
            }, {
                status: 400
            });
        }
        // 检查文件是否存在
        const availableFiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableNovels"])();
        if (!availableFiles.includes(filename)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '文件不存在'
            }, {
                status: 404
            });
        }
        // 检查是否已经解析过
        if (!reparse && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNovelParsed"])(filename)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '该小说已经解析过，如需重新解析请设置reparse=true'
            }, {
                status: 409
            });
        }
        const novelsDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), '..', 'novels');
        const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(novelsDir, filename);
        let result;
        if (reparse) {
            result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reparseNovel"])(filename);
        } else {
            result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$novel$2d$parser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseNovelFile"])(filePath);
        }
        if (!result) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '解析失败'
            }, {
                status: 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: result,
            message: `成功解析小说《${result.novel.title}》，共${result.chapters.length}章`
        });
    } catch (error) {
        console.error('解析小说失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '解析小说失败'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__8a485315._.js.map